import React from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import ZoomInIcon from "@mui/icons-material/ZoomIn";
import ZoomOutIcon from "@mui/icons-material/ZoomOut";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { useLanguage } from "../../../i18n/LanguageContext";

interface ZoomControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  buttonStyle?: React.CSSProperties;
}

export const ZoomControls: React.FC<ZoomControlsProps> = ({
  onZoomIn,
  onZoomOut,
  onResetZoom,
  buttonStyle,
}) => {
  const { t } = useLanguage();

  const baseButtonStyle = {
    color: "text.secondary",
    "&:hover": {
      backgroundColor: "action.hover",
    },
    ...buttonStyle,
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 0.5,
        height: "100%",
        px: 1,
      }}
    >
      <Tooltip title={`${t("zoom_in")} (Ctrl++)`} arrow>
        <IconButton
          onClick={onZoomIn}
          size="small"
          aria-label={`${t("zoom_in")} (Ctrl++)`}
          tabIndex={0}
          sx={{
            ...baseButtonStyle,
            "&:focus-visible": {
              outline: "2px solid",
              outlineColor: "primary.main",
              boxShadow: "0 0 0 4px rgba(25, 118, 210, 0.2)",
            },
          }}
        >
          <ZoomInIcon sx={{ fontSize: 24 }} />
        </IconButton>
      </Tooltip>
      <Tooltip title={`${t("zoom_out")} (Ctrl+-)`} arrow>
        <IconButton
          onClick={onZoomOut}
          size="small"
          aria-label={`${t("zoom_out")} (Ctrl+-)`}
          tabIndex={0}
          sx={{
            ...baseButtonStyle,
            "&:focus-visible": {
              outline: "2px solid",
              outlineColor: "primary.main",
              boxShadow: "0 0 0 4px rgba(25, 118, 210, 0.2)",
            },
          }}
        >
          <ZoomOutIcon sx={{ fontSize: 24 }} />
        </IconButton>
      </Tooltip>
      <Tooltip title={`${t("fit_to_screen")} (Ctrl+0)`} arrow>
        <IconButton
          onClick={onResetZoom}
          size="small"
          aria-label={`${t("fit_to_screen")} (Ctrl+0)`}
          tabIndex={0}
          sx={{
            ...baseButtonStyle,
            "&:focus-visible": {
              outline: "2px solid",
              outlineColor: "primary.main",
              boxShadow: "0 0 0 4px rgba(25, 118, 210, 0.2)",
            },
          }}
        >
          <RestartAltIcon sx={{ fontSize: 24 }} />
        </IconButton>
      </Tooltip>
    </Box>
  );
};
