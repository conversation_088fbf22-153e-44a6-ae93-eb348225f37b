import React from "react";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../store";
import {
  Snackbar,
  Alert,
  AlertTitle,
  Box,
  LinearProgress,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

/**
 * 全局加载提示（Snackbar风格，可悬浮/可关闭）
 * - 监听 Store.startGlobalLoading / finishGlobalLoading 管理的全局加载集合
 * - 非阻塞式展示在右下角
 * - 用户可点击关闭，直到下一次全局加载重新出现
 */
const GlobalLoadingSnackbar: React.FC = observer(() => {
  const store = React.useContext(StoreContext);

  const [open, setOpen] = React.useState(false);
  const [dismissed, setDismissed] = React.useState(false);

  // 当全局加载结束时，重置关闭状态；当再次开始时，如果未被手动关闭则打开
  React.useEffect(() => {
    if (store.hasGlobalLoading) {
      if (!dismissed) setOpen(true);
    } else {
      setOpen(false);
      setDismissed(false);
    }
  }, [store.hasGlobalLoading, dismissed]);

  const handleClose = () => {
    setOpen(false);
    setDismissed(true);
  };

  const loadingCount = (() => {
    try {
      return store.loadingElements ? store.loadingElements.size : 0;
    } catch {
      return 0;
    }
  })();

  const message = store.globalLoadingMessage || "Loading elements...";
  const progress = store.globalLoadingProgress; // 0-100 或 null
  const stage = store.globalLoadingStage; // 可能为空串

  const showDeterminate = typeof progress === "number";
  const progressValue = showDeterminate
    ? Math.max(0, Math.min(100, progress))
    : 0;

  return (
    <Snackbar
      open={open}
      onClose={handleClose}
      anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      autoHideDuration={null} // 持续展示，直到用户关闭或加载结束
    >
      <Alert
        severity="info"
        variant="filled"
        sx={{ minWidth: 300, maxWidth: 460, boxShadow: 3 }}
        action={
          <IconButton
            aria-label="close"
            color="inherit"
            size="small"
            onClick={handleClose}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      >
        <AlertTitle>Loading</AlertTitle>
        <Box
          sx={{ display: "flex", flexDirection: "column", gap: 0.5, mb: 0.5 }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Box
              sx={{
                fontSize: 14,
                lineHeight: 1.4,
                wordBreak: "break-word",
                flex: 1,
              }}
            >
              {message}
            </Box>
            {loadingCount > 1 && (
              <Box
                component="span"
                sx={{
                  fontSize: 12,
                  opacity: 0.9,

                  borderRadius: 1,
                  px: 0.5,
                  py: 0.25,
                }}
              >
                {loadingCount} items
              </Box>
            )}
          </Box>
          {!!stage && <Box sx={{ fontSize: 12, opacity: 0.9 }}>{stage}</Box>}
        </Box>
        <LinearProgress
          variant={showDeterminate ? "determinate" : "indeterminate"}
          value={progressValue}
          color="inherit"
        />
      </Alert>
    </Snackbar>
  );
});

export default GlobalLoadingSnackbar;
