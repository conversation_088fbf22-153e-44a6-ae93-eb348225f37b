import { useCallback, useState } from "react";
import { getUid } from "../utils";

// 统一的加载状态管理接口
interface LoadingState {
  [key: string]: boolean;
}

// 媒体元素配置接口
interface MediaElementConfig {
  type: "image" | "video" | "gif" | "audio";
  src: string;
  metadata?: any;
  crossOrigin?: boolean;
  timeout?: number;
}

// 非媒体元素配置接口
interface NonMediaElementConfig {
  type: "text" | "shape";
  data: any;
}

// 统一的元素配置类型
type ElementConfig = MediaElementConfig | NonMediaElementConfig;

// 点击添加处理结果
interface ClickHandlerResult {
  success: boolean;
  elementId?: string;
  error?: string;
}

// 统一的点击添加处理Hook
export const useElementClickHandler = (store: any) => {
  const [loadingStates, setLoadingStates] = useState<LoadingState>({});

  // 统一的加载状态管理
  const setLoading = useCallback((key: string, loading: boolean) => {
    setLoadingStates((prev) => {
      if (loading) {
        return { ...prev, [key]: true };
      } else {
        const newState = { ...prev };
        delete newState[key];
        return newState;
      }
    });
  }, []);

  // 统一的媒体DOM元素创建
  const createMediaElement = useCallback(
    (config: MediaElementConfig): Promise<HTMLElement> => {
      return new Promise((resolve, reject) => {
        const { type, src, crossOrigin = false, timeout = 30000 } = config;

        let element: HTMLElement;
        let eventType: string;

        // 根据类型创建对应的DOM元素
        switch (type) {
          case "image":
          case "gif":
            element = document.createElement("img");
            eventType = "load";
            if (crossOrigin) {
              (element as HTMLImageElement).crossOrigin = "anonymous";
            }
            break;
          case "video":
            element = document.createElement("video");
            eventType = "loadedmetadata";
            break;
          case "audio":
            element = document.createElement("audio");
            eventType = "loadedmetadata";
            break;
          default:
            reject(new Error(`Unsupported media type: ${type}`));
            return;
        }

        // 设置基本属性
        (element as any).src = src;
        element.style.display = "none";
        document.body.appendChild(element);

        // 设置超时处理
        const timeoutId = setTimeout(() => {
          cleanup();
          reject(new Error(`Loading timeout for ${type}: ${src}`));
        }, timeout);

        // 清理函数
        const cleanup = () => {
          clearTimeout(timeoutId);
          element.removeEventListener(eventType, onSuccess);
          element.removeEventListener("error", onError);
        };

        // 成功回调
        const onSuccess = () => {
          cleanup();
          resolve(element);
        };

        // 错误回调
        const onError = (e: Event) => {
          cleanup();
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
          reject(new Error(`Failed to load ${type}: ${src}`));
        };

        // 绑定事件
        element.addEventListener(eventType, onSuccess);
        element.addEventListener("error", onError);
      });
    },
    []
  );

  // 统一的媒体元素添加处理
  const handleMediaElementAdd = useCallback(
    async (
      config: MediaElementConfig,
      loadingKey: string
    ): Promise<ClickHandlerResult> => {
      const elementId = getUid();
      setLoading(loadingKey, true);

      try {
        // 创建并加载媒体元素
        const mediaElement = await createMediaElement(config);
        mediaElement.id = `${config.type}-${elementId}`;

        // 根据类型调用对应的store方法
        switch (config.type) {
          case "image":
            store.addImageElement(
              mediaElement as HTMLImageElement,
              elementId,
              config.metadata
            );
            break;
          case "video":
            store.addVideoElement(
              mediaElement as HTMLVideoElement,
              elementId,
              config.metadata
            );
            break;
          case "gif":
            store.addGifElement(
              mediaElement as HTMLImageElement,
              elementId,
              config.metadata
            );
            break;
          case "audio":
            // 假设有addAudioElement方法
            if (store.addAudioElement) {
              store.addAudioElement(
                mediaElement as HTMLAudioElement,
                elementId,
                config.metadata
              );
            }
            break;
        }

        return { success: true, elementId };
      } catch (error) {
        console.error(`Error adding ${config.type}:`, error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : `Failed to add ${config.type}`,
        };
      } finally {
        setLoading(loadingKey, false);
      }
    },
    [store, createMediaElement, setLoading]
  );

  // 统一的非媒体元素添加处理
  const handleNonMediaElementAdd = useCallback(
    (config: NonMediaElementConfig): ClickHandlerResult => {
      try {
        switch (config.type) {
          case "text":
            store.addText(config.data);
            break;
          case "shape":
            store.addShapeElement(config.data);
            break;
          default:
            throw new Error(`Unsupported element type: ${config.type}`);
        }

        // 保存变更
        if (store.saveChange) {
          store.saveChange("add_element");
        }

        return { success: true };
      } catch (error) {
        console.error(`Error adding ${config.type}:`, error);
        return {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : `Failed to add ${config.type}`,
        };
      }
    },
    [store]
  );

  // 统一的元素添加处理入口
  const handleElementAdd = useCallback(
    async (
      config: ElementConfig,
      loadingKey?: string
    ): Promise<ClickHandlerResult> => {
      if ("src" in config) {
        // 媒体元素
        return handleMediaElementAdd(
          config,
          loadingKey || `${config.type}-${Date.now()}`
        );
      } else {
        // 非媒体元素
        return handleNonMediaElementAdd(config);
      }
    },
    [handleMediaElementAdd, handleNonMediaElementAdd]
  );

  return {
    loadingStates,
    handleElementAdd,
    setLoading,
  };
};

// 导出类型定义
export type {
  ElementConfig,
  MediaElementConfig,
  NonMediaElementConfig,
  ClickHandlerResult,
  LoadingState,
};
