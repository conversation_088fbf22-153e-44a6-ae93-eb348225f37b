import {
  externalApiClient,
  internalApiClient,
  handleHttpError,
} from "../config/httpClient";
import { jamendoApiKey } from "../config";
import type { AxiosError } from "axios";

// Jamendo API 配置
const API_KEY = jamendoApiKey;
const BASE_URL = "https://api.jamendo.com/v3.0";
const apiBaseUrl =
  process.env.REACT_APP_API_BASE_URL || "http://localhost:8080";
// Jamendo代理URL构建函数

/**
 * 将Jamendo音频URL转换为代理URL以解决跨域问题
 * @param jamendoUrl 原始Jamendo音频URL
 * @returns 代理后的URL
 */
export const getProxiedJamendoUrl = (jamendoUrl: string): string => {
  if (
    !jamendoUrl ||
    (!jamendoUrl.includes("jamendo.com") && !jamendoUrl.includes("jamen.do"))
  ) {
    return jamendoUrl; // 如果不是Jamendo URL，直接返回原URL
  }
  return `${apiBaseUrl}/api/proxy/jamendo-audio?url=${encodeURIComponent(
    jamendoUrl
  )}`;
};

/**
 * 统一规范化音频源地址
 * - 对 Jamendo 音频源进行代理转换
 * - 对已是代理地址的保持不变
 * - 其他来源不做改动
 */
export const normalizeAudioSrc = (src: string): string => {
  if (!src) return src;
  // 已经是我们后端代理
  if (src.includes("/api/proxy/jamendo-audio")) return src;
  // Jamendo 直链需要代理
  if (src.includes("jamendo.com") || src.includes("jamen.do")) {
    return getProxiedJamendoUrl(src);
  }
  return src;
};

// 音频类型接口
export interface JamendoTrack {
  id: string;
  name: string;
  duration: number;
  artist_name: string;
  audiodownload: string; // 音频下载链接
  image: string; // 封面图片
}

// API响应接口
export interface JamendoApiResponse {
  headers: {
    status: string;
    code: number;
    error_message: string;
    results_count: number;
    total_count: number; // 总结果数，用于分页
  };
  results: any[];
}

// 获取热门音乐
export const getPopularTracks = async (
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await externalApiClient.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          boost: "popularity_total",
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 打印完整响应以检查结构
    console.log("Jamendo API 响应:", response.data);

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error("获取Jamendo音乐失败:", httpError);
    return { tracks: [], total: 0 };
  }
};

// 搜索音乐
export const searchTracks = async (
  query: string,
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await externalApiClient.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          namesearch: query,
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error("搜索Jamendo音乐失败:", httpError);
    return { tracks: [], total: 0 };
  }
};

// 根据标签获取音乐
export const getTracksByTags = async (
  tags: string,
  limit: number = 20,
  offset: number = 0
): Promise<{ tracks: JamendoTrack[]; total: number }> => {
  try {
    const response = await externalApiClient.get<JamendoApiResponse>(
      `${BASE_URL}/tracks/`,
      {
        params: {
          client_id: API_KEY,
          format: "json",
          limit,
          offset,
          tags: tags,
          include: "musicinfo",
          audioformat: "mp32",
        },
      }
    );

    // 计算总数，若API返回不正确则使用合理的默认值
    let totalCount = 0;
    if (
      response.data.headers &&
      typeof response.data.headers.total_count === "number"
    ) {
      totalCount = response.data.headers.total_count;
    } else if (
      response.data.headers &&
      typeof response.data.headers.results_count === "number"
    ) {
      // 备选：使用results_count乘以一个因子作为估计总数
      totalCount = response.data.headers.results_count * 10;
    } else {
      // 如果无法获取总数，则假设至少有100条数据
      totalCount = Math.max(100, response.data.results.length);
    }

    // 确保返回的总数至少比当前加载的结果数量多
    totalCount = Math.max(
      totalCount,
      offset + response.data.results.length + 1
    );

    return {
      tracks: response.data.results.map((track: any) => ({
        id: track.id,
        name: track.name,
        duration: track.duration,
        artist_name: track.artist_name,
        audiodownload: track.audiodownload,
        image: track.image,
      })),
      total: totalCount,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error("获取标签音乐失败:", httpError);
    return { tracks: [], total: 0 };
  }
};
