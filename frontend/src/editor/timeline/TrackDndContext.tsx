import React, {
  useCallback,
  useState,
  createContext,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragMoveEvent,
  MeasuringStrategy,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { StoreContext } from "../../store";
import { useContext } from "react";
import { EditorElement, Track, TrackType } from "../../types";

// 拖拽方向类型
type DragDirection = "up" | "down" | "none";

// 拖拽激活配置常量
const DRAG_ACTIVATION_DISTANCE = 10; // 激活拖拽所需的最小移动距离（像素）
const DRAG_TOLERANCE = 10; // 拖拽容差（像素）
const DRAG_DELAY = 10; // 拖拽延迟（毫秒）
const DIRECTION_THRESHOLD = 10; // 方向判断阈值（像素）
const THROTTLE_INTERVAL = 16; // 16ms ≈ 60fps，优化拖拽性能

// 泳道间隔类型
interface TrackGap {
  id: string;
  beforeTrackId: string | null;
  afterTrackId: string | null;
  trackType: TrackType;
}

// 创建拖拽上下文
interface TrackDndContextValue {
  activeElement: EditorElement | null;
  activeTrack: Track | null;
  overTrack: Track | null;
  overGap: TrackGap | null;
  isCreatingNewTrack: boolean;
  isElementDragging: boolean;
}

// 拖拽状态类型
interface DragState {
  activeElement: EditorElement | null;
  activeTrack: Track | null;
  overTrack: Track | null;
  overGap: TrackGap | null;
  isCreatingNewTrack: boolean;
  dragDirection: DragDirection;
  isElementDragging: boolean;
}

export const TrackDndStateContext = createContext<TrackDndContextValue>({
  activeElement: null,
  activeTrack: null,
  overTrack: null,
  overGap: null,
  isCreatingNewTrack: false,
  isElementDragging: false,
});

// 导出上下文钩子
export const useTrackDnd = () => useContext(TrackDndStateContext);

interface TrackDndContextProps {
  children: React.ReactNode;
}

// 防抖工具函数
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流工具函数
const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

export const TrackDndContext: React.FC<TrackDndContextProps> = ({
  children,
}) => {
  const store = useContext(StoreContext);

  // 合并相关的拖拽状态，减少状态更新触发的重渲染
  const [dragState, setDragState] = useState<DragState>({
    activeElement: null,
    activeTrack: null,
    overTrack: null,
    overGap: null,
    isCreatingNewTrack: false,
    dragDirection: "none",
    isElementDragging: false,
  });

  // 引用存储
  const trackGapsRef = useRef<TrackGap[]>([]);
  const initialDragPositionRef = useRef<{ x: number; y: number } | null>(null);
  const lastDragMoveTimeRef = useRef(0);
  const highlightedGapRef = useRef<string | null>(null);
  const pendingStateUpdateRef = useRef<Partial<DragState> | null>(null);
  const rafIdRef = useRef<number | null>(null);

  // DOM元素缓存
  const gapElementsCache = useRef<Map<string, HTMLElement>>(new Map());

  // 缓存轨道查询
  const tracksMapRef = useRef<Map<string, Track>>(new Map());

  // 使用 useMemo 优化轨道间隔计算
  const elementTypeFromActive = useMemo(() => {
    if (!dragState.activeElement) return null;

    // 获取当前元素类型
    if (
      dragState.activeElement.type === "image" ||
      dragState.activeElement.type === "video"
    ) {
      return "media" as TrackType;
    }
    return dragState.activeElement.type as TrackType;
  }, [dragState.activeElement]);

  // 优化轨道映射缓存
  useEffect(() => {
    const tracksMap = new Map<string, Track>();
    store.trackManager.tracks.forEach((track) => {
      tracksMap.set(track.id, track);
    });
    tracksMapRef.current = tracksMap;
  }, [store.trackManager.tracks]);

  // 生成轨道间隔，使用 useMemo 优化性能
  const trackGaps = useMemo(() => {
    if (!dragState.activeElement || !elementTypeFromActive) {
      return [];
    }

    // 获取所有轨道
    const tracks = store.trackManager.tracks;
    if (!tracks || tracks.length === 0) return [];

    const elementType = elementTypeFromActive;
    const gaps: TrackGap[] = [];

    // 添加第一个轨道之前的间隔
    gaps.push({
      id: `gap-before-${tracks[0].id}`,
      beforeTrackId: null,
      afterTrackId: tracks[0].id,
      trackType: elementType,
    });

    // 添加轨道之间的间隔
    for (let i = 0; i < tracks.length - 1; i++) {
      gaps.push({
        id: `gap-between-${tracks[i].id}-${tracks[i + 1].id}`,
        beforeTrackId: tracks[i].id,
        afterTrackId: tracks[i + 1].id,
        trackType: elementType,
      });
    }

    // 添加最后一个轨道之后的间隔
    gaps.push({
      id: `gap-after-${tracks[tracks.length - 1].id}`,
      beforeTrackId: tracks[tracks.length - 1].id,
      afterTrackId: null,
      trackType: elementType,
    });

    // 添加最终底部间隙，防止无限滚动
    gaps.push({
      id: `gap-final-bottom`,
      beforeTrackId: tracks[tracks.length - 1].id,
      afterTrackId: null,
      trackType: elementType,
    });

    return gaps;
  }, [
    dragState.activeElement,
    store.trackManager.tracks,
    elementTypeFromActive,
  ]);

  // 更新trackGapsRef当trackGaps变化时
  useEffect(() => {
    trackGapsRef.current = trackGaps;
  }, [trackGaps]);

  // 配置拖拽传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 优化拖拽激活配置
      activationConstraint: {
        distance: DRAG_ACTIVATION_DISTANCE,
        tolerance: DRAG_TOLERANCE,
        delay: DRAG_DELAY,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // 优化的DOM元素获取函数
  const getGapElement = useCallback((gapId: string): HTMLElement | null => {
    // 先检查缓存
    if (gapElementsCache.current.has(gapId)) {
      const cachedElement = gapElementsCache.current.get(gapId)!;
      // 检查元素是否仍在DOM中
      if (document.contains(cachedElement)) {
        return cachedElement;
      } else {
        // 清理无效缓存
        gapElementsCache.current.delete(gapId);
      }
    }

    // 查询DOM并缓存
    const element = document.getElementById(gapId);
    if (element) {
      gapElementsCache.current.set(gapId, element);
    }
    return element;
  }, []);

  // 批量状态更新函数
  const batchedUpdateDragState = useCallback((newState: Partial<DragState>) => {
    // 合并待更新状态
    pendingStateUpdateRef.current = {
      ...pendingStateUpdateRef.current,
      ...newState,
    };

    // 取消之前的requestAnimationFrame
    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current);
    }

    // 使用requestAnimationFrame批量更新状态
    rafIdRef.current = requestAnimationFrame(() => {
      if (pendingStateUpdateRef.current) {
        setDragState((prev) => ({
          ...prev,
          ...pendingStateUpdateRef.current!,
        }));
        pendingStateUpdateRef.current = null;
      }
      rafIdRef.current = null;
    });
  }, []);

  // 优化的高亮处理函数
  const updateGapHighlight = useCallback(
    (newHighlightedGapId: string | null) => {
      const currentHighlighted = highlightedGapRef.current;

      if (currentHighlighted === newHighlightedGapId) {
        return; // 没有变化，直接返回
      }

      // 移除旧的高亮
      if (currentHighlighted) {
        const oldElement = getGapElement(currentHighlighted);
        if (oldElement) {
          oldElement.classList.remove("track-gap-highlight");
        }
      }

      // 添加新的高亮
      if (newHighlightedGapId) {
        const newElement = getGapElement(newHighlightedGapId);
        if (newElement) {
          newElement.classList.add("track-gap-highlight");
        }
      }

      highlightedGapRef.current = newHighlightedGapId;
    },
    [getGapElement]
  );

  // 清理所有高亮样式
  const clearAllHighlights = useCallback(() => {
    // 批量移除高亮样式
    trackGapsRef.current.forEach((gap) => {
      const gapElement = getGapElement(gap.id);
      if (gapElement) {
        gapElement.classList.remove("track-gap-highlight");
      }
    });
    highlightedGapRef.current = null;
  }, [getGapElement]);

  // 处理拖拽开始
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const elementId = active.id as string;

      // 查找被拖拽的元素
      const element = store.editorElements.find((el) => el.id === elementId);
      if (!element) return;

      // 查找元素所在的轨道
      const track = store.trackManager.getTrackByElementId(elementId);
      if (!track) return;

      // 使用批量状态更新
      batchedUpdateDragState({
        activeElement: element,
        activeTrack: track,
        dragDirection: "none",
        isElementDragging: true,
      });

      // 记录初始拖拽位置
      initialDragPositionRef.current = { x: 0, y: 0 };

      // 添加全局拖拽样式
      document.body.classList.add("track-dragging-active");

      // 如果正在播放，暂停播放
      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [store, batchedUpdateDragState]
  );

  // 节流的拖拽移动处理函数
  const throttledHandleDragMove = useMemo(
    () =>
      throttle((event: DragMoveEvent) => {
        const { over } = event;
        const now = performance.now();

        // 跳过过于频繁的更新
        if (now - lastDragMoveTimeRef.current < THROTTLE_INTERVAL) {
          return;
        }
        lastDragMoveTimeRef.current = now;

        if (!over) {
          batchedUpdateDragState({
            overTrack: null,
            overGap: null,
            isCreatingNewTrack: false,
          });
          updateGapHighlight(null);
          return;
        }

        const overId = over.id as string;

        // 计算拖拽方向（使用防抖，避免频繁更新）
        if (initialDragPositionRef.current && event.delta) {
          const deltaY = event.delta.y;
          if (Math.abs(deltaY) > DIRECTION_THRESHOLD) {
            batchedUpdateDragState({
              dragDirection: deltaY > 0 ? "down" : "up",
            });
          }
        }

        // 检查是否悬停在轨道上
        if (overId.startsWith("track-")) {
          const id = overId.replace("track-", "");
          const track = tracksMapRef.current.get(id);
          if (track) {
            batchedUpdateDragState({
              overTrack: track,
              overGap: null,
              isCreatingNewTrack: false,
            });
            updateGapHighlight(null);
          }
        }
        // 检查是否悬停在轨道间隔上
        else if (overId.startsWith("gap-")) {
          // 查找匹配的间隔
          const gap = trackGapsRef.current.find((g) => g.id === overId);
          if (gap) {
            batchedUpdateDragState({
              overTrack: null,
              overGap: gap,
              isCreatingNewTrack: true,
            });
            updateGapHighlight(overId);
          }
        } else {
          batchedUpdateDragState({
            overTrack: null,
            overGap: null,
            isCreatingNewTrack: false,
          });
          updateGapHighlight(null);
        }
      }, THROTTLE_INTERVAL),
    [batchedUpdateDragState, updateGapHighlight]
  );

  // 处理拖拽移动
  const handleDragMove = useCallback(
    (event: DragMoveEvent) => {
      throttledHandleDragMove(event);
    },
    [throttledHandleDragMove]
  );

  // 获取要创建的轨道类型
  const getTrackTypeForElement = useCallback(
    (element: EditorElement): TrackType => {
      if (["image", "video", "shape", "gif"].includes(element.type)) {
        return "media";
      }
      if (element.type === "text") {
        return "text";
      }
      return element.type as TrackType;
    },
    []
  );

  // 清理拖拽状态的通用函数
  const cleanupDragState = useCallback(() => {
    // 取消待处理的requestAnimationFrame
    if (rafIdRef.current) {
      cancelAnimationFrame(rafIdRef.current);
      rafIdRef.current = null;
    }

    // 移除全局拖拽样式
    document.body.classList.remove("track-dragging-active");

    // 清理所有高亮样式
    clearAllHighlights();

    // 清理DOM缓存
    gapElementsCache.current.clear();

    // 重置状态
    setDragState({
      activeElement: null,
      activeTrack: null,
      overTrack: null,
      overGap: null,
      isCreatingNewTrack: false,
      dragDirection: "none",
      isElementDragging: false,
    });

    initialDragPositionRef.current = null;
    pendingStateUpdateRef.current = null;
  }, [clearAllHighlights]);

  // 处理拖拽取消
  const handleDragCancel = useCallback(() => {
    cleanupDragState();
  }, [cleanupDragState]);

  // 处理拖拽结束
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!active || !over || !dragState.activeElement) {
        cleanupDragState();
        return;
      }

      const elementId = active.id as string;
      const overId = over.id.toString();
      const elementType = getTrackTypeForElement(dragState.activeElement);

      // 检查是否放置在轨道上
      if (overId.startsWith("track-")) {
        const targetTrackId = overId.replace("track-", "");
        const targetTrack = tracksMapRef.current.get(targetTrackId);

        // 如果目标轨道与原轨道不同，则处理元素移动
        if (
          dragState.activeTrack &&
          dragState.activeTrack.id !== targetTrackId &&
          targetTrack
        ) {
          // 检查元素类型是否与目标轨道类型匹配
          let isTypeMatch = false;

          // 对于image、video、shape、gif类型的元素，它们可以放在media类型的轨道中
          if (
            ["image", "video", "shape", "gif"].includes(
              dragState.activeElement.type
            ) &&
            targetTrack.type === "media"
          ) {
            isTypeMatch = true;
          } else if (
            dragState.activeElement.type === "text" &&
            targetTrack.type === "text"
          ) {
            isTypeMatch = true;
          } else if (
            dragState.activeElement.type === "audio" &&
            targetTrack.type === "audio"
          ) {
            isTypeMatch = true;
          } else {
            isTypeMatch = false;
          }

          if (isTypeMatch) {
            // 类型匹配，直接移动元素到目标轨道
            store.trackManager.moveElementToTrack(elementId, targetTrackId);
          } else {
            // 类型不匹配，根据拖拽方向创建新轨道
            const targetTrackIndex = store.trackManager.tracks.findIndex(
              (t) => t.id === targetTrackId
            );

            if (targetTrackIndex !== -1) {
              let newTrackPosition = targetTrackIndex;

              // 根据拖拽方向确定新轨道的位置
              if (dragState.dragDirection === "down") {
                // 向下拖动，在目标轨道下方创建新轨道
                newTrackPosition = targetTrackIndex + 1;
              } else if (dragState.dragDirection === "up") {
                // 向上拖动，在目标轨道上方创建新轨道
                newTrackPosition = targetTrackIndex;
              }

              const newTrackType = elementType;

              // 创建新轨道并将元素移动到新轨道
              const newTrack = store.trackManager.createTrackAtPosition(
                newTrackType,
                newTrackPosition,
                `${
                  newTrackType.charAt(0).toUpperCase() + newTrackType.slice(1)
                } Track ${store.trackManager.getTrackCountByType(newTrackType)}`
              );

              // 将元素移动到新轨道
              store.trackManager.moveElementToTrack(elementId, newTrack.id);
            } else {
              // 如果找不到目标轨道索引，直接移动到目标轨道
              store.trackManager.moveElementToTrack(elementId, targetTrackId);
            }
          }

          // 保存更改
          store.saveChange("modify_track");
        }
      }
      // 检查是否放置在轨道间隔上，需要创建新轨道
      else if (
        overId.startsWith("gap-") &&
        dragState.isCreatingNewTrack &&
        dragState.overGap
      ) {
        // 创建新轨道
        let newTrackPosition = -1;

        // 确定新轨道的位置
        if (dragState.overGap.beforeTrackId && dragState.overGap.afterTrackId) {
          // 在两个轨道之间插入
          const beforeIndex = store.trackManager.tracks.findIndex(
            (t) => t.id === dragState.overGap.beforeTrackId
          );

          if (beforeIndex !== -1) {
            newTrackPosition = beforeIndex + 1;
          }
        } else if (dragState.overGap.beforeTrackId) {
          // 在最后一个轨道之后插入
          const beforeIndex = store.trackManager.tracks.findIndex(
            (t) => t.id === dragState.overGap.beforeTrackId
          );

          if (beforeIndex !== -1) {
            newTrackPosition = beforeIndex + 1;
          }
        } else if (dragState.overGap.afterTrackId) {
          // 在第一个轨道之前插入
          newTrackPosition = 0;
        }

        const newTrackType = elementType;

        // 创建新轨道并将元素移动到新轨道
        const newTrack = store.trackManager.createTrackAtPosition(
          newTrackType,
          newTrackPosition
        );

        // 将元素移动到新轨道
        store.trackManager.moveElementToTrack(elementId, newTrack.id);

        // 保存更改
        store.saveChange("modify_track");
      }

      // 检查并删除所有空轨道
      store.trackManager.removeEmptyTracks();

      // 根据轨道顺序更新Canvas上的元素显示顺序
      store.updateCanvasOrderByTrackOrder();

      // 清理拖拽状态
      cleanupDragState();
    },
    [dragState, store, getTrackTypeForElement, cleanupDragState]
  );

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
      gapElementsCache.current.clear();
    };
  }, []);

  // 创建上下文值
  const contextValue = useMemo(
    () => ({
      activeElement: dragState.activeElement,
      activeTrack: dragState.activeTrack,
      overTrack: dragState.overTrack,
      overGap: dragState.overGap,
      isCreatingNewTrack: dragState.isCreatingNewTrack,
      isElementDragging: dragState.isElementDragging,
    }),
    [dragState]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
      modifiers={[restrictToVerticalAxis]}
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.Always,
        },
      }}
      autoScroll={false} // 禁用自动滚动，防止无限滚动问题
    >
      <TrackDndStateContext.Provider value={contextValue}>
        {children}
      </TrackDndStateContext.Provider>
    </DndContext>
  );
};

// 全局元素拖拽状态管理
let globalElementDragState = false;
const elementDragListeners = new Set<() => void>();

export const setGlobalElementDragging = (isDragging: boolean) => {
  globalElementDragState = isDragging;
  elementDragListeners.forEach((listener) => listener());
};

export const useGlobalElementDragState = () => {
  const [isDragging, setIsDragging] = useState(globalElementDragState);

  useEffect(() => {
    const listener = () => setIsDragging(globalElementDragState);
    elementDragListeners.add(listener);
    return () => {
      elementDragListeners.delete(listener);
    };
  }, []);

  return isDragging;
};
