---
inclusion: manual
---
# 项目结构指南

本项目是一个在线视频平台，Fabric 视频编辑器是一款功能强大的基于 Web 的视频编辑应用程序，它利用 Fabric.js 的功能提供灵活直观的基于画布的编辑体验。该项目结合了 React 前端和 Node.js 后端，提供了全面的视频编辑工具套件，包括时间轴操作、元素管理和动画控制。

## 目录结构

### 前端 (`frontend/`)
- `src/`: 源代码目录
  - `app/`: 应用程序核心逻辑
  - `assets/`: 静态资源文件
  - `components/`: React UI 组件
  - `data/`: 数据模型和常量
  - `editor/`: 视频编辑器核心组件
  - `hooks/`: 自定义 React Hooks
  - `interfaces/`: TypeScript 接口定义
  - `services/`: API 服务和外部服务集成
  - `store/`: MobX 状态管理
  - `tests/`: 测试文件
  - `theme/`: 主题和样式配置
  - `utils/`: 工具函数
  - `App.tsx`: 主应用程序组件
  - `index.tsx`: 应用程序入口点
  - `types.ts`: 全局类型定义

### 后端 (`server/`)
- `src/`: 服务器源代码
  - `controllers/`: API 控制器
  - `ffmpeg/`: FFmpeg 命令生成和处理
  - `services/`: 业务逻辑服务
  - `tests/`: 测试文件
  - `utils/`: 工具函数
  - `index.ts`: 服务器入口点
  - `types.ts`: 类型定义
- `assets/`: 服务器静态资源
- `logs/`: 日志文件
- `output/`: 输出文件目录

## 重要文件
- `README.md`: 项目说明文档
- `ARCHITECTURE.md`: 架构设计文档
- `package.json`: 项目依赖配置
- `tsconfig.json`: TypeScript 配置

## 开发规范
1. 前端代码统一放在 `frontend` 目录下，遵循模块化组织
2. 后端代码统一放在 `server` 目录下，按功能模块划分
3. 所有新功能开发都应该遵循现有的目录结构
4. 保持目录结构清晰，避免过深的嵌套
5. 相关功能的代码应该放在同一目录下
