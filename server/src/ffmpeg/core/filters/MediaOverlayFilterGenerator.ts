import { TextFilterGenerator } from "./TextFilterGenerator";
import { MediaElement } from "../../types";

interface TransitionTiming {
  fadeInEndTime: number;
  fadeOutStartTime: number;
  inTransitionDuration: number;
  outTransitionDuration: number;
}

interface FilterContext {
  element: MediaElement;
  index: number;
  canvasWidth: number;
  canvasHeight: number;
  duration: number;
  prefix: string;
}

/**
 * 媒体叠加滤镜生成器
 * 负责生成用于在视频上叠加媒体元素(文本、图片等)的FFmpeg滤镜命令
 * 支持淡入淡出等过渡效果
 */
export class MediaOverlayFilterGenerator {
  /**
   * 默认过渡动画持续时间（秒）
   * 用于控制媒体元素的淡入淡出效果的默认时长
   */
  private readonly DEFAULT_TRANSITION_DURATION = 2;

  /**
   * 构造函数
   * @param textFilterGenerator 文本滤镜生成器，用于处理文本类型的媒体元素
   */
  constructor(private readonly textFilterGenerator: TextFilterGenerator) {}

  /**
   * 生成媒体叠加滤镜
   * 主方法：根据媒体元素配置生成完整的FFmpeg滤镜命令
   *
   * @param element 媒体元素对象
   * @param index 元素索引
   * @param canvasWidth 画布宽度
   * @param canvasHeight 画布高度
   * @param duration 总视频持续时间
   * @param prefix 滤镜命名前缀
   * @returns 完整的FFmpeg滤镜命令数组
   */
  public generateMediaOverlayFilters(
    element: MediaElement,
    index: number,
    canvasWidth: number,
    canvasHeight: number,
    duration: number,
    prefix: string
  ): string[] {
    // 创建滤镜上下文对象
    const context: FilterContext = {
      element,
      index,
      canvasWidth,
      canvasHeight,
      duration,
      prefix,
    };

    // 将毫秒转换为秒，并保留两位小数
    const startTime = Number((element.timeFrame.start / 1000).toFixed(2));
    const endTime = Number((element.timeFrame.end / 1000).toFixed(2));
    const elementDuration = endTime - startTime;

    // 获取淡入淡出过渡效果类型，默认为fade
    const fadeInTransition = element.transition?.in || "fade";
    const fadeOutTransition = element.transition?.out || "fade";

    // 获取前端传递的动画持续时间，优先使用分别的入场和出场时间
    const customInDuration = element.transition?.inDuration;
    const customOutDuration = element.transition?.outDuration;
    const customTransitionDuration = element.transition?.duration; // 向后兼容

    // 计算淡入淡出的时间点
    const {
      fadeInEndTime,
      fadeOutStartTime,
      inTransitionDuration,
      outTransitionDuration,
    } = this.getFadeTimings(
      startTime,
      endTime,
      customInDuration,
      customOutDuration,
      customTransitionDuration
    );

    // 生成滤镜链
    return this.buildFilterChain(
      context,
      startTime,
      endTime,
      elementDuration,
      fadeInTransition,
      fadeOutTransition,
      fadeInEndTime,
      fadeOutStartTime,
      inTransitionDuration,
      outTransitionDuration
    );
  }

  /**
   * 构建完整的滤镜链
   * 根据过渡效果类型和时间点组装滤镜命令
   */
  private buildFilterChain(
    context: FilterContext,
    startTime: number,
    endTime: number,
    elementDuration: number,
    fadeInTransition: string,
    fadeOutTransition: string,
    fadeInEndTime: number,
    fadeOutStartTime: number,
    inTransitionDuration: number,
    outTransitionDuration: number
  ): string[] {
    console.log(`构建滤镜链 - 元素ID: ${context.element.id}`);
    console.log(
      `过渡效果: 入场=${fadeInTransition}(${inTransitionDuration}s), 出场=${fadeOutTransition}(${outTransitionDuration}s)`
    );
    console.log(
      `时间范围: ${startTime}s - ${endTime}s (持续${elementDuration}s)`
    );

    // 生成基础叠加滤镜
    let filters = this.generateBaseOverlay(context, startTime, elementDuration);

    // 跟踪当前处理的输入和输出流名称
    let currentInput = `${context.prefix}_base${context.index}`;
    let currentOutput = currentInput;

    // 当两个过渡效果都为none时，不添加任何动画效果
    // 直接使用基础叠加滤镜，并添加时间控制滤镜确保元素在startTime显示和endTime结束显示
    if (fadeInTransition === "none" && fadeOutTransition === "none") {
      console.log(`元素 ${context.element.id} 没有设置动画效果，跳过xfade滤镜`);
      filters.push(
        this.createTimingFilter(context, currentOutput, startTime, endTime)
      );
      return filters;
    }

    // 处理淡入效果
    if (fadeInTransition !== "none") {
      filters = filters.concat(
        this.generateFadeInEffect(
          context,
          startTime,
          inTransitionDuration,
          fadeInTransition
        )
      );
      // 更新当前输入/输出流名称
      currentInput = `${context.prefix}_active${context.index}`;
      currentOutput = currentInput;
    }

    // 处理淡出效果
    if (fadeOutTransition !== "none") {
      filters = filters.concat(
        this.generateFadeOutEffect(
          context,
          fadeOutStartTime,
          outTransitionDuration,
          fadeOutTransition,
          currentInput
        )
      );
      // 更新当前输出流名称
      currentOutput = `${context.prefix}_final${context.index}`;
    }

    // 添加时间控制滤镜
    filters.push(
      this.createTimingFilter(context, currentOutput, startTime, endTime)
    );

    return filters;
  }

  /**
   * 创建时间控制滤镜
   * 使用trim滤镜确保媒体只在指定时间范围内显示
   * 注意：此时输入的视频流已经经过了VideoFilterGenerator的处理，
   * 包括mediaStartTime的裁剪和播放速度调整
   */
  private createTimingFilter(
    context: FilterContext,
    inputStream: string,
    startTime: number,
    endTime: number
  ): string {
    // 使用Math.floor确保时间戳精度一致，避免小数点精度问题
    const flooredStartTime = Math.floor(startTime * 100) / 100;
    // 确保结束时间不超过元素的实际结束时间
    const actualEndTime = context.element.timeFrame.end / 1000;
    const flooredEndTime = Math.min(
      Math.floor(endTime * 100) / 100,
      Math.floor(actualEndTime * 100) / 100
    );

    // 计算视频流的实际持续时间
    const streamDuration = flooredEndTime - flooredStartTime;

    console.log(
      `创建时间控制滤镜: 输入流=${inputStream}, 开始时间=${startTime.toFixed(
        2
      )}, 结束时间=${flooredEndTime.toFixed(
        2
      )}, 流持续时间=${streamDuration.toFixed(2)}s`
    );

    // 对于已经处理过的视频流，我们只需要：
    // 1. 限制流的持续时间为所需的时长
    // 2. 设置正确的时间戳偏移，使其在正确的时间显示

    // 由于VideoFilterGenerator已经处理了mediaStartTime和播放速度，
    // 这里的输入流从时间戳0开始，持续时间为streamDuration
    // 我们需要将其时间戳偏移到startTime
    return `[${inputStream}]trim=start=0:duration=${streamDuration.toFixed(
      3
    )},setpts=PTS-STARTPTS+${flooredStartTime.toFixed(2)}/TB[${
      context.prefix
    }_timed${context.index}]`;
  }

  /**
   * 计算淡入淡出的时间点
   * 根据元素的开始和结束时间，计算淡入结束时间点和淡出开始时间点
   * @param startTime 元素开始时间（秒）
   * @param endTime 元素结束时间（秒）
   * @param customInDuration 前端传递的入场动画持续时间（秒），可选
   * @param customOutDuration 前端传递的出场动画持续时间（秒），可选
   * @param customDuration 前端传递的通用动画持续时间（秒），向后兼容，可选
   */
  private getFadeTimings(
    startTime: number,
    endTime: number,
    customInDuration?: number,
    customOutDuration?: number,
    customDuration?: number
  ): TransitionTiming {
    // 计算元素总持续时间
    const duration = endTime - startTime;

    // 计算入场动画持续时间
    let inTransitionDuration: number;
    if (customInDuration !== undefined && customInDuration > 0) {
      inTransitionDuration = Math.min(customInDuration, duration / 3);
      console.log(
        `使用前端传递的入场动画持续时间: ${customInDuration}秒，实际使用: ${inTransitionDuration}秒`
      );
    } else if (customDuration !== undefined && customDuration > 0) {
      // 向后兼容：如果没有指定入场时间但有通用时间，使用通用时间
      inTransitionDuration = Math.min(customDuration, duration / 3);
      console.log(
        `使用前端传递的通用动画持续时间作为入场时间: ${customDuration}秒，实际使用: ${inTransitionDuration}秒`
      );
    } else {
      inTransitionDuration = Math.min(
        this.DEFAULT_TRANSITION_DURATION,
        duration / 3
      );
      console.log(`使用默认入场动画持续时间: ${inTransitionDuration}秒`);
    }

    // 计算出场动画持续时间
    let outTransitionDuration: number;
    if (customOutDuration !== undefined && customOutDuration > 0) {
      outTransitionDuration = Math.min(customOutDuration, duration / 3);
      console.log(
        `使用前端传递的出场动画持续时间: ${customOutDuration}秒，实际使用: ${outTransitionDuration}秒`
      );
    } else if (customDuration !== undefined && customDuration > 0) {
      // 向后兼容：如果没有指定出场时间但有通用时间，使用通用时间
      outTransitionDuration = Math.min(customDuration, duration / 3);
      console.log(
        `使用前端传递的通用动画持续时间作为出场时间: ${customDuration}秒，实际使用: ${outTransitionDuration}秒`
      );
    } else {
      outTransitionDuration = Math.min(
        this.DEFAULT_TRANSITION_DURATION,
        duration / 3
      );
      console.log(`使用默认出场动画持续时间: ${outTransitionDuration}秒`);
    }

    return {
      // 淡入结束时间 = 开始时间 + 入场过渡持续时间
      fadeInEndTime: startTime + inTransitionDuration + 0.1,
      // 淡出开始时间 = 结束时间 - 出场过渡持续时间，确保不早于淡入结束时间
      fadeOutStartTime: Math.max(
        startTime + inTransitionDuration,
        endTime - outTransitionDuration - 0.1
      ),
      inTransitionDuration,
      outTransitionDuration,
    };
  }

  /**
   * 生成基础媒体叠加滤镜
   * 创建透明背景并在其上叠加媒体元素
   */
  private generateBaseOverlay(
    context: FilterContext,
    startTime: number,
    elementDuration: number
  ): string[] {
    const filters: string[] = [];

    // 创建透明背景，并确保使用带alpha的像素格式
    filters.push(this.createTransparentBackground(context, "1"));

    // 在透明背景上叠加媒体/文本
    const overlayContent = this.generateOverlayContent(
      context,
      startTime,
      elementDuration
    );
    // 将叠加结果输出到命名为[prefix_baseN]的流
    filters.push(
      `[${context.prefix}_bg1_${context.index}]${overlayContent}[${context.prefix}_base${context.index}]`
    );

    return filters;
  }

  /**
   * 创建透明背景滤镜
   * 生成一个与画布大小相同的透明背景，用于叠加媒体元素
   */
  private createTransparentBackground(
    context: FilterContext,
    suffix: string
  ): string {
    const startTime = Number(
      (context.element.timeFrame.start / 1000).toFixed(2)
    );
    return `color=c=black@0:s=${Math.round(context.canvasWidth)}x${Math.round(
      context.canvasHeight
    )}:d=${context.duration}[${context.prefix}_bg${suffix}_${context.index}]`;
  }

  /**
   * 生成媒体叠加内容
   * 根据媒体元素类型生成相应的叠加滤镜
   */
  private generateOverlayContent(
    context: FilterContext,
    startTime: number,
    elementDuration: number
  ): string {
    // 如果是文本类型，使用文本滤镜生成器处理
    if (context.element.type === "text") {
      return this.textFilterGenerator.generateFilter(
        0,
        context.element,
        startTime,
        elementDuration,
        context.index
      );
    }

    // 获取元素的位置和旋转信息
    const placement = context.element.placement;
    let x = Number(placement?.x || 0);
    let y = Number(placement?.y || 0);
    const rotation = placement?.rotation || 0;
    const originalWidth = Number(placement?.width || 0);
    const originalHeight = Number(placement?.height || 0);

    // 处理旋转元素的定位，适用于所有旋转角度
    if (
      rotation &&
      (context.element.type === "image" || context.element.type === "video")
    ) {
      // 步骤1: 获取原始元素中心点
      const centerX = x + originalWidth / 2;
      const centerY = y + originalHeight / 2;

      // 步骤2: 计算旋转角度（标准化到0-360度范围）
      const normalizedRotation = ((rotation % 360) + 360) % 360;
      const rotationRad = (normalizedRotation * Math.PI) / 180;

      // 步骤3: 计算旋转后的宽高
      const cosTheta = Math.abs(Math.cos(rotationRad));
      const sinTheta = Math.abs(Math.sin(rotationRad));
      const rotatedWidth = Math.round(
        originalWidth * cosTheta + originalHeight * sinTheta
      );
      const rotatedHeight = Math.round(
        originalWidth * sinTheta + originalHeight * cosTheta
      );

      // 步骤4: 计算旋转后图像在包围盒中的偏移
      // 注意：这里我们直接计算旋转矩形的包围盒，不再使用中间变量
      // 通过计算原始矩形顶点旋转后的位置
      const halfWidth = originalWidth / 2;
      const halfHeight = originalHeight / 2;

      // 步骤5: 计算旋转后的四个角（相对于旋转中心）
      const originalCorners = [
        { x: -halfWidth, y: -halfHeight }, // 左上
        { x: halfWidth, y: -halfHeight }, // 右上
        { x: halfWidth, y: halfHeight }, // 右下
        { x: -halfWidth, y: halfHeight }, // 左下
      ];

      const rotatedCorners = originalCorners.map((p) => {
        return {
          x: p.x * Math.cos(rotationRad) - p.y * Math.sin(rotationRad),
          y: p.x * Math.sin(rotationRad) + p.y * Math.cos(rotationRad),
        };
      });

      // 步骤6: 找出旋转后区域的边界
      const minX = Math.min(...rotatedCorners.map((p) => p.x));
      const maxX = Math.max(...rotatedCorners.map((p) => p.x));
      const minY = Math.min(...rotatedCorners.map((p) => p.y));
      const maxY = Math.max(...rotatedCorners.map((p) => p.y));

      // 步骤7: 计算旋转中心相对于包围盒左上角的偏移
      const offsetX = halfWidth + minX;
      const offsetY = halfHeight + minY;

      // 步骤8: 最终计算叠加坐标，确保旋转后的图像中心与原始中心对齐
      x = centerX - rotatedWidth / 2 + offsetX;
      y = centerY - rotatedHeight / 2 + offsetY;

      console.log(`旋转元素详情: ID=${context.element.id}`);
      console.log(
        `原始数据: 位置=(${placement?.x || 0}, ${
          placement?.y || 0
        }), 尺寸=${originalWidth}x${originalHeight}, 角度=${rotation}°`
      );
      console.log(
        `标准化角度: ${normalizedRotation}° (${rotationRad.toFixed(
          4
        )}弧度), cos=${Math.cos(rotationRad).toFixed(4)}, sin=${Math.sin(
          rotationRad
        ).toFixed(4)}`
      );
      console.log(`旋转后尺寸: ${rotatedWidth}x${rotatedHeight}`);
      console.log(
        `旋转边界: 左上(${minX.toFixed(2)}, ${minY.toFixed(
          2
        )}) 右下(${maxX.toFixed(2)}, ${maxY.toFixed(2)})`
      );
      console.log(
        `中心点平移: 旧中心(${centerX.toFixed(2)}, ${centerY.toFixed(
          2
        )}) 偏移(${offsetX.toFixed(2)}, ${offsetY.toFixed(2)})`
      );
      console.log(`最终叠加坐标: (${x.toFixed(2)}, ${y.toFixed(2)})`);
    }

    // 检查是否是视频元素且有播放速度调整
    const isVideo = context.element.type === "video";
    const hasPlaybackSpeed =
      context.element.playbackSpeed !== undefined &&
      context.element.playbackSpeed !== 1;

    // 记录叠加内容的参数
    const logPlaybackSpeed =
      context.element.playbackSpeed !== undefined
        ? context.element.playbackSpeed
        : 1;

    console.log(
      `生成媒体叠加内容: 类型=${context.element.type}, 位置=(${x.toFixed(
        2
      )},${y.toFixed(2)}), 播放速度=${logPlaybackSpeed}`
    );

    // 对于视频元素，可能需要特殊处理
    if (isVideo) {
      const playbackSpeed =
        context.element.playbackSpeed !== undefined
          ? context.element.playbackSpeed
          : 1;

      console.log(
        `处理视频元素叠加: ID=${context.element.id}, 播放速度=${playbackSpeed}`
      );

      // 确保视频元素的叠加位置正确
      return `[${context.prefix}${context.index}]overlay=${x.toFixed(
        2
      )}:${y.toFixed(2)}:format=auto`;
    } else {
      // 其他类型（如图片）使用标准叠加
      return `[${context.prefix}${context.index}]overlay=${x.toFixed(
        2
      )}:${y.toFixed(2)}`;
    }
  }

  /**
   * 生成淡入效果滤镜
   * 使用xfade滤镜创建从透明到可见的淡入效果
   */
  private generateFadeInEffect(
    context: FilterContext,
    startTime: number,
    transitionDuration: number,
    transition: string
  ): string[] {
    const filters: string[] = [];

    // 创建用于淡入效果的透明背景
    filters.push(
      this.createTransparentBackground(context, "2"),
      // 创建空流作为淡入的源
      `[${context.prefix}_bg2_${context.index}]format=rgba,null[${context.prefix}_fadein_overlay${context.index}]`,
      // 使用xfade滤镜实现从空到基础叠加的过渡
      // offset设置为0，因为淡入效果应该立即开始，时间控制通过后续的时间戳偏移处理
      `[${context.prefix}_fadein_overlay${context.index}][${context.prefix}_base${context.index}]xfade=transition=${transition}:duration=${transitionDuration}:offset=0[${context.prefix}_active${context.index}]`
    );

    console.log(
      `生成淡入效果: transition=${transition}, duration=${transitionDuration}s, offset=0 (立即开始)`
    );

    return filters;
  }

  /**
   * 生成淡出效果滤镜
   * 使用xfade滤镜创建从可见到透明的淡出效果
   */
  private generateFadeOutEffect(
    context: FilterContext,
    fadeOutStartTime: number,
    transitionDuration: number,
    transition: string,
    currentInput: string
  ): string[] {
    const filters: string[] = [];

    // 计算淡出效果在视频流中的相对开始时间
    // fadeOutStartTime是绝对时间，需要转换为相对于元素开始时间的偏移
    const elementStartTime = context.element.timeFrame.start / 1000;
    const relativeOffset = fadeOutStartTime - elementStartTime;

    // 创建用于淡出效果的透明背景
    filters.push(
      this.createTransparentBackground(context, "3"),
      // 创建空流作为淡出的目标
      `[${context.prefix}_bg3_${context.index}]format=rgba,null[${context.prefix}_fadeout_overlay${context.index}]`,
      // 使用xfade滤镜实现从当前内容到空的过渡
      // offset使用相对偏移时间，因为视频流已经被时间戳偏移处理
      `[${currentInput}][${context.prefix}_fadeout_overlay${
        context.index
      }]xfade=transition=${transition}:duration=${transitionDuration}:offset=${relativeOffset.toFixed(
        2
      )}[${context.prefix}_final${context.index}]`
    );

    console.log(
      `生成淡出效果: transition=${transition}, duration=${transitionDuration}s, 绝对开始时间=${fadeOutStartTime}s, 相对偏移=${relativeOffset.toFixed(
        2
      )}s`
    );

    return filters;
  }
}
