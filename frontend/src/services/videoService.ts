import { externalApiClient, handleHttpError } from "../config/httpClient";
import { pexelsApiKey, pixabayApi<PERSON>ey } from "../config";
import type { AxiosError } from "axios";

// 定义视频源类型
export type VideoSource = "pexels" | "pixabay";

// API 配置
const PEXELS_API_KEY = pexelsApiKey;
const PIXABAY_API_KEY = pixabayApiKey;
const PIXABAY_API_URL = "https://pixabay.com/api/videos/";

const PER_PAGE = 10; // 每页视频数量

// 视频接口
export interface VideoFile {
  id: string;
  video_files: Array<{ link: string }>;
  duration: number;
}

// API响应接口
export interface VideoApiResponse {
  total: number;
  videos: VideoFile[];
}

// 将Pixabay视频转换为统一格式
const mapPixabayVideosToCommonFormat = (videos: any[]): VideoFile[] => {
  return videos.map((video) => {
    // Pixabay返回多种分辨率，我们使用large作为主要视频
    const videoUrl =
      video.videos.large.url ||
      video.videos.medium.url ||
      video.videos.small.url;
    return {
      id: video.id.toString(),
      video_files: [{ link: videoUrl }],
      duration: video.duration || 0,
    };
  });
};

// 将Pexels视频转换为统一格式
const mapPexelsVideosToCommonFormat = (videos: any[]): VideoFile[] => {
  return videos.map((video) => ({
    id: video.id,
    video_files: video.video_files,
    duration: video.duration,
  }));
};

// 获取视频（通用方法）
export const getVideos = async (
  source: VideoSource,
  page: number = 1,
  searchQuery: string = "",
  perPage: number = PER_PAGE
): Promise<VideoApiResponse> => {
  try {
    let response;
    let videos: VideoFile[] = [];
    let total = 0;

    if (source === "pexels") {
      // Pexels API
      const endpoint = searchQuery
        ? `https://api.pexels.com/videos/search?query=${encodeURIComponent(
            searchQuery
          )}&per_page=${perPage}&page=${page}`
        : `https://api.pexels.com/videos/popular?per_page=${perPage}&page=${page}`;

      response = await externalApiClient.get(endpoint, {
        headers: {
          Authorization: PEXELS_API_KEY,
        },
      });

      videos = mapPexelsVideosToCommonFormat(response.data.videos);
      total = response.data.total_results || 0;
    } else {
      // Pixabay API
      const endpoint = `${PIXABAY_API_URL}?key=${PIXABAY_API_KEY}&per_page=${perPage}&page=${page}`;
      const queryParam = searchQuery
        ? `&q=${encodeURIComponent(searchQuery)}`
        : "";

      response = await externalApiClient.get(`${endpoint}${queryParam}`);
      videos = mapPixabayVideosToCommonFormat(response.data.hits);
      total = response.data.totalHits || 0;
    }

    return {
      videos,
      total,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error(`获取${source}视频失败:`, httpError);
    return { videos: [], total: 0 };
  }
};

// 根据标签获取视频
export const getVideosByTag = async (
  source: VideoSource,
  tag: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<VideoApiResponse> => {
  return getVideos(source, page, tag, perPage);
};

// 获取视频标签配置
export const getVideoTags = () => {
  return {
    common: [
      "nature",
      "people",
      "business",
      "technology",
      "food",
      "travel",
      "sports",
      "education",
    ],
    pixabay: [
      "backgrounds",
      "animation",
      "fashion",
      "science",
      "health",
      "music",
      "places",
      "animals",
    ],
    pexels: [
      "aerial",
      "time lapse",
      "slow motion",
      "3d",
      "abstract",
      "urban",
      "vintage",
      "cinematic",
    ],
  };
};
