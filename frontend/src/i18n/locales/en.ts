// English translations
const enTranslations = {
  // Common
  theme: "Theme",
  language: "Language",
  light_mode: "Light Mode",
  dark_mode: "Dark Mode",
  settings: "Settings",
  chinese: "Chinese",
  english: "English",
  text: "Text",

  // Navbar
  undo: "Undo",
  redo: "Redo",
  export: "Export",
  shortcuts: "Shortcuts",
  keyboard_shortcuts: "Keyboard Shortcuts",
  shortcut: "Shortcut",
  close: "Close",
  video_editor_logo: "Video Editor Logo",

  // Editing
  move_mode: "Move Mode",
  hand_tool: "Hand Tool",
  edit_mode: "Edit Mode",
  zoom_in: "Zoom In",
  zoom_out: "Zoom Out",
  fit_to_screen: "Fit to Screen",

  // Project
  untitled_project: "Untitled Project",
  save_project: "Save Project",
  project_name: "Project Name",
  untitled_video: "Untitled Video",

  // Export
  export_video: "Export Video",
  export_format: "Export Format",
  export_quality: "Export Quality",
  export_settings: "Export Settings",
  start_export: "Start Export",
  preparing_export: "Preparing export...",
  cancel: "Cancel",
  download_video: "Download Video",
  retry: "Retry",
  restart: "Restart",

  // Video Processing Status
  generating_video: "Generating video...",
  generation_complete: "Generation Complete",
  generation_failed: "Generation Failed",
  cancelled: "Cancelled",

  // Video Processing Stages
  initializing: "Initializing",
  detecting_audio: "Detecting Audio",
  detecting_audio_elements: "Analyzing Audio Elements",
  processing_elements: "Processing Elements",
  processing_audio_elements: "Processing Audio",
  processing_visual_elements: "Processing Visual Elements",
  processing_captions: "Processing Captions",
  building_command: "Building Command",
  generating_command: "Generating Command",
  rendering: "Rendering",
  finalizing: "Finalizing",
  completed: "Completed",
  failed: "Failed",

  // Loading
  loading: "Loading...",
  processing: "Processing...",

  // Unified Loading Snackbar
  loading_elements: "Loading elements...",
  loading_snackbar_global_title: "Loading Elements",
  loading_snackbar_basic_title: "Processing Task",
  loading_snackbar_processing_task: "Processing your task...",
  items_count: "{0} items",
  action_saving: "Saving",
  action_loading: "Loading",
  action_processing: "Processing",
  action_uploading: "Uploading",
  action_downloading: "Downloading",
  action_rendering: "Rendering",
  action_generating: "Generating",
  action_importing: "Importing",
  action_exporting: "Exporting",
  action_completing_import: "Completing import",
  action_refreshing: "Refreshing",
  action_updating: "Updating",
  action_creating: "Creating",
  action_deleting: "Deleting",
  stage_saving: "Saving",
  stage_loading: "Loading",
  stage_processing: "Processing",
  stage_uploading: "Uploading",
  stage_downloading: "Downloading",
  stage_rendering: "Rendering",
  stage_generating: "Generating",
  stage_importing: "Importing",
  stage_exporting: "Exporting",

  // Shortcut Categories
  category_edit: "Edit",
  category_tools: "Tools",
  category_view: "View",
  category_project: "Project",
  category_help: "Help",
  category_playback: "Playback",
  keyboard_navigation: "Use Tab key to navigate shortcuts",
  press_key: "Press key",

  // Shortcut Descriptions
  shortcut_undo: "Undo",
  shortcut_redo: "Redo",
  shortcut_move_mode: "Move Mode",
  shortcut_hand_tool: "Hand Tool",
  shortcut_zoom_in: "Zoom In",
  shortcut_zoom_out: "Zoom Out",
  shortcut_fit_to_screen: "Fit to Screen",
  shortcut_save_project: "Save Project",
  shortcut_export_video: "Export Video",
  shortcut_show_shortcuts: "Show Shortcuts",
  shortcut_delete_selected: "Delete Selected Element",
  shortcut_delete_element: "Delete Element",
  shortcut_duplicate_selected: "Duplicate Selected Element",
  shortcut_play_pause: "Play/Pause",
  shortcut_seek_forward: "Seek Forward",
  shortcut_seek_backward: "Seek Backward",
  shortcut_fit_timeline: "Fit Timeline",

  // Video Formats
  format_mp4: "MP4 Video",
  format_gif: "GIF Animation",
  format_mov: "MOV Video",
  format_mp3: "MP3 Audio",

  // Export quality
  quality_high: "High Quality",
  quality_medium: "Medium Quality",
  quality_low: "Low Quality",
  quality_high_desc: "Best quality, larger file size",
  quality_medium_desc: "Balanced quality and file size",
  quality_low_desc: "Smaller file size, lower quality",

  // Video Aspect Ratios
  ratio: "Ratio",
  ratio_selection_description:
    "Choose the right video ratio and platforms for your content",
  original: "Original",
  aspect_ratio_16_9: "16:9",
  aspect_ratio_4_3: "4:3",
  aspect_ratio_2_1: "2:1",
  aspect_ratio_9_16: "9:16",
  aspect_ratio_1_1: "1:1",
  aspect_ratio_3_4: "3:4",

  // Video Background
  background: "Background",
  recents: "Recents",
  recommended: "Recommended",
  transparent: "Transparent",
  no_background: "No Background",

  // Base Settings
  no_element_selected: "No element selected",
  alignment: "Alignment",
  position: "Position",
  lock: "Lock",
  unlock: "Unlock",
  opacity: "Opacity",
  clone: "Clone",
  delete: "Delete",
  fullscreen: "Fullscreen",
  flip_horizontal: "Flip Horizontal",
  flip_vertical: "Flip Vertical",
  align_left: "Align Left",
  align_center: "Align Center",
  align_right: "Align Right",
  justify: "Justify",
  align_top: "Align Top",
  align_middle: "Align Middle",
  align_bottom: "Align Bottom",
  position_x: "Position X",
  position_y: "Position Y",
  width: "Width",
  height: "Height",
  rotation: "Rotation",

  // Canvas Settings
  canvas_settings: "Canvas Settings",
  basic: "Basic",
  advanced: "Advanced",
  random_color: "Random Color",
  gradient: "Gradient",
  duration: "Duration",
  seconds: "seconds",
  import: "Import",

  // Font Settings
  font_setting: "Font Setting",
  font_family: "Font Family",
  font_size: "Font Size",
  font_color: "Font Color",
  text_align: "Text Align",
  styles: "Styles",
  char_spacing: "Character Spacing",
  line_height: "Line Height",
  bold: "Bold",
  italic: "Italic",
  underline: "Underline",
  strikethrough: "Strikethrough",
  stroke_width: "Stroke Width",
  stroke_color: "Stroke Color",
  shadow_color: "Shadow Color",
  shadow_blur: "Shadow Blur",
  shadow_offset_x: "Shadow Offset X",
  shadow_offset_y: "Shadow Offset Y",
  use_gradient: "Use Gradient",
  gradient_start: "Gradient Start",
  gradient_end: "Gradient End",
  text_style: "Text Style",
  subtitle: "Subtitle",
  position_settings_hint:
    "Position settings (you can drag subtitles directly on canvas to adjust position)",
  horizontal_position: "Horizontal Position",
  vertical_position: "Vertical Position",
  position_hint:
    "Tip: Positive values move right/down, negative values move left/up",
  debug_position: "Debug Subtitle Position",
  debug_position_hint:
    "Click this button to view subtitle position information in console and verify frontend-backend position consistency",

  // Image Settings
  image_settings: "Image",
  crop_image: "Crop Image",
  apply_crop: "Apply Crop",
  cancel_crop: "Cancel Crop",
  show_settings: "Show Settings",
  hide_settings: "Hide Settings",
  border_width: "Border Width",

  // GIF Settings
  gif_settings: "GIF Settings",
  select_gif_element: "Please select a GIF element",
  crop_gif: "Crop GIF",
  gif_info: "GIF Information",
  animated: "Animated",
  frame_count: "Frame Count",
  frame_rate: "Frame Rate",
  yes: "Yes",
  no: "No",
  enable_border: "Enable Border",
  border_color: "Border Color",
  border_style: "Border Style",
  border_radius: "Border Radius",
  solid: "Solid",
  dashed: "Dashed",
  dotted: "Dotted",
  effects: "Effects",
  brightness: "Brightness",
  contrast: "Contrast",
  saturation: "Saturation",
  hue: "Hue",
  blur: "Blur",
  reset_filters: "Reset Filters",

  // Video Settings
  video_settings: "Video Settings",
  crop_video: "Crop Video",
  playback_speed: "Playback Speed",
  volume: "Volume",
  mute: "Mute",
  video: "Video",

  // Effect Types
  none: "None",
  black_and_white: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",

  // Shape Settings
  shape_properties: "Shape Properties",
  shape_type: "Shape Type",

  // Shapes related
  shapes: "Shapes",

  // Shape categories
  shape_category_all: "All",
  shape_category_basic: "Basic",
  shape_category_polygons: "Polygons",
  shape_category_arrows: "Arrows",
  shape_category_stars: "Stars",
  shape_category_symbols: "Symbols",
  shape_category_special: "Special",

  // Shape names
  shape_rect: "Rectangle",
  shape_rounded_rect: "Square",
  shape_circle: "Circle",
  shape_ellipse: "Ellipse",
  shape_triangle: "Triangle",
  shape_line: "Line",
  shape_diamond: "Diamond",
  shape_pentagon: "Pentagon",
  shape_hexagon: "Hexagon",
  shape_octagon: "Octagon",
  shape_right_arrow: "Right Arrow",
  shape_up_arrow: "Up Arrow",
  shape_down_arrow: "Down Arrow",
  shape_cross: "Cross",
  shape_star: "Star",
  shape_four_point_star: "Four Point Star",
  shape_six_point_star: "Six Point Star",
  shape_eight_point_star: "Eight Point Star",
  shape_sun_burst: "Sun Burst",
  shape_semicircle: "Semicircle",
  shape_quarter_circle: "Quarter Circle",
  shape_ring: "Ring",
  shape_half_ring: "Half Ring",
  shape_plus: "Plus",
  shape_arch: "Arch",
  shape_parallelogram: "Parallelogram",
  rectangle: "Rectangle",
  rounded_rectangle: "Rounded Rectangle",
  circle: "Circle",
  ellipse: "Ellipse",
  triangle: "Triangle",
  line: "Line",
  pentagon: "Pentagon",
  hexagon: "Hexagon",
  octagon: "Octagon",
  parallelogram: "Parallelogram",
  arch: "Arch",
  fill_color: "Fill Color",
  background_color: "Background Color",

  // Filter Settings
  filters: "Filters",
  border_settings: "Border Settings",
  color: "Color",
  style: "Style",
  radius: "Radius",
  effect_type: "Effect Type",
  presets: "Presets",
  default: "Default",
  warm: "Warm",
  cool: "Cool",
  vintage: "Vintage",
  sharp: "Sharp",
  soft: "Soft",
  custom: "Custom",
  adjust: "Adjust",

  // Animation Settings
  animations: "Animations",
  animation_in: "In",
  animation_out: "Out",
  fade: "Fade",
  slide_down: "Slide Down",
  slide_up: "Slide Up",
  slide_left: "Slide Left",
  slide_right: "Slide Right",
  wipe_down: "Wipe Down",
  wipe_up: "Wipe Up",
  wipe_left: "Wipe Left",
  wipe_right: "Wipe Right",
  breathe: "Breathe",
  direction: "Direction",
  left: "Left",
  right: "Right",
  top: "Top",
  bottom: "Bottom",
  use_mask: "Use Mask",

  // Menu Items
  uploads: "Uploads",
  layers: "Layers",
  Video: "Video",
  Audio: "Audio",
  Image: "Image",
  Gif: "GIF",
  Text: "Text",
  Caption: "Caption",
  Shape: "Shape",

  // Elements Panel
  move_up: "Move Up",
  move_down: "Move Down",
  move_to_top: "Move to Top",
  move_to_bottom: "Move to Bottom",

  // Uploads
  your_media: "Your media",
  project: "Project",
  workspace: "Workspace",
  upload_media: "Upload Media",
  upload_media_description: "Upload videos, images, or audio files",
  upload_to_workspace: "Upload to Workspace",
  upload_workspace_description: "Drag and drop files here or click to browse",
  workspace_assets_available:
    "Workspace assets will be available across projects",

  // Text Resources
  text_title: "Title",
  text_subtitle: "Subtitle",
  text_body: "Body",
  text_caption: "Caption",

  // Text Style Categories
  text_category_title: "Title Styles",
  text_category_subtitle: "Subtitle Styles",
  text_category_body: "Body Styles",
  text_category_annotation: "Annotation Styles",
  text_category_social: "Social Media",
  text_category_creative: "Creative Styles",

  // Text Style Descriptions
  text_desc_main_title: "Main Title",
  text_desc_subtitle: "Subtitle",
  text_desc_body: "Body Text",
  text_desc_caption: "Caption Text",
  text_desc_creative_title: "Comic Style",
  text_desc_elegant_title: "Handwriting Style",
  text_desc_tech: "Monospace Font",
  text_desc_classic_serif: "Classic Serif",
  text_desc_chapter_title: "Clear chapter divider title",
  text_desc_brand_title: "Modern brand title",
  text_desc_standard_subtitle: "Clear and readable general subtitle",
  text_desc_cinema_subtitle: "Movie theater style subtitle",
  text_desc_news_subtitle: "News broadcast subtitle",
  text_desc_documentary_subtitle: "Documentary professional subtitle",
  text_desc_bilingual_subtitle: "Bilingual subtitle style",

  // Text Interface
  text_search_placeholder: "Search text styles...",
  text_no_results: "No matching text styles found",
  text_try_different_keywords: "Try using different keywords",
  text_category_effects: "Effect Styles",

  // Image Library
  image_library: "Image Library",
  search_images: "Search images...",
  no_images_found: "No images found. Try another search term.",
  loading_images_failed:
    "Failed to load images from {0}, please check your network connection or try again later",
  no_images_found_try_another:
    "No images found, please try another search term or tag",
  displayed_results: "Displayed {0} / {1} results",
  unknown: "Unknown",

  // GIF Library
  gif_library: "GIF Library",
  search_gifs: "Search GIFs...",
  no_gifs_found: "No GIFs found. Try another search term.",
  load_gifs_error:
    "Failed to load GIFs, please check your network connection or try again later",
  add_gif_error: "Failed to add GIF, please try again",
  adding: "Adding...",
  add_to_canvas: "Add to Canvas",
  trending: "Trending",
  search: "Search",
  random: "Random",
  categories: "Categories",
  load_more_random: "Load More Random GIFs",
  no_more_gifs: "No more GIFs available",

  // Video Library
  // Video Library
  video_library: "Video Library",
  search_videos: "Search videos...",
  no_videos_found: "No videos found. Try a different search term.",
  loading_videos_failed:
    "Failed to load videos from {0}, please check your network connection or try again later",
  no_videos_found_try_another:
    "No videos found, please try another search term or tag",
  displayed_all_results: "Displayed all {0} results",
  video_loading_timeout: "Video loading timeout",
  video_loading_failed: "Failed to load video",
  video_preview: "Video Preview",
  adding_video: "Adding...",
  video_added_successfully: "Video added successfully!",
  video_add_failed: "Failed to add video, please try again",

  // Video Tag Categories
  video_tag_nature: "nature",
  video_tag_people: "people",
  video_tag_business: "business",
  video_tag_technology: "technology",
  video_tag_food: "food",
  video_tag_travel: "travel",
  video_tag_sports: "sports",
  video_tag_education: "education",
  video_tag_backgrounds: "backgrounds",
  video_tag_animation: "animation",
  video_tag_fashion: "fashion",
  video_tag_science: "science",
  video_tag_health: "health",
  video_tag_music: "music",
  video_tag_places: "places",
  video_tag_animals: "animals",
  video_tag_aerial: "aerial",
  video_tag_time_lapse: "time lapse",
  video_tag_slow_motion: "slow motion",
  video_tag_3d: "3d",
  video_tag_abstract: "abstract",
  video_tag_urban: "urban",
  video_tag_vintage: "vintage",
  video_tag_cinematic: "cinematic",

  // Video Sources
  video_source_pixabay: "Pixabay",
  video_source_pexels: "Pexels",

  // Video States
  video_loading: "Loading",
  video_error: "Error",
  video_ready: "Ready",

  // Duration Format
  minutes: "minutes",
  minute: "minute",
  seconds_short: "sec",

  // Audio Library
  audio_library: "Audio Library",
  local_audio: "Local Audio",
  search_placeholder: "Search...",
  no_local_audio: "Local audio library is empty.",
  no_music_found: "No music found. Try searching or selecting different tags.",
  audio_loading_failed: "Audio loading failed, please try another audio",
  all_results_displayed: "Displayed all {0} results",
  loading_popular_failed:
    "Failed to load popular tracks. Please try again later.",
  loading_jamendo_failed:
    "Failed to load Jamendo music. Please check your network connection or API key.",
  no_tag_music_found: "No music found for the selected tags.",
  tag_loading_failed: "Failed to load music by tags. Please try again later.",
  search_failed: "Failed to search Jamendo music. Please try again later.",
  no_search_result: "No music found matching your search query.",
  audio_playback_error:
    "Audio playback failed. Please check the audio file or network connection.",

  // Captions
  captions: "Captions",
  add_caption: "Add Caption",
  clear_all_captions: "Clear All Captions",
  clear_confirm_title: "Clear All Captions",
  clear_confirm_message:
    "Are you sure you want to delete all captions? This action cannot be undone.",
  clear_all: "Clear All",
  no_text: "No text",
  upload_srt: "Upload SRT file",
  export_captions: "Export captions",
  insert_caption: "Insert caption",
  merge_captions: "Merge captions",
  play_segment: "Play this segment",
  delete_caption: "Delete caption",
  format_should_be: "Format should be HH:MM:SS",
  invalid_time: "Invalid time value",
  start_time_less: "Start time must be less than end time",
  end_time_greater: "End time must be greater than start time",
  time_overlaps: "Time overlaps with adjacent captions",
  suggested_time: "Suggested time {0} for {1} time",
  start_time: "start",
  end_time: "end",

  // Timeline Components
  timeline_edit: "Edit",
  timeline_copy: "Copy",
  timeline_cut: "Cut",
  timeline_delete: "Delete",
  timeline_preview: "Preview",
  timeline_adjust_style: "Adjust Style",
  timeline_auto_translate: "Auto Translate",
  timeline_start: "Start",
  timeline_end: "End",

  // SeekPlayer component
  seekplayer_delete: "Delete",
  seekplayer_split_element: "Split Element",
  seekplayer_seek_backward: "Seek Backward",
  seekplayer_seek_forward: "Seek Forward",
  seekplayer_play: "Play",
  seekplayer_pause: "Pause",
  seekplayer_play_pause: "Play/Pause",
  seekplayer_zoom_in: "Zoom In",
  seekplayer_zoom_out: "Zoom Out",
  seekplayer_fit_view: "Fit to View",
  zoom_level: "Zoom Level",

  // AI Assistant
  ai_assistant: "AI Assistant",
  ai_welcome_message: "Hello! I'm your AI assistant. How can I help you today?",
  ai_sample_response:
    "Thank you for your question! I'll do my best to help you with video editing tasks.",
  ai_quick_actions: "Suggestions",
  ai_quick_action_help: "How to use the video editor?",
  ai_quick_action_suggestions: "Give me some design suggestions",
  ai_quick_action_export: "How to export video?",
  ai_typing: "AI is handling your request...",
  ai_input_placeholder: "Type your question here...",

  // Platform Compatibility
  platforms: "Platforms",
  format_description: "Format Description",
  platform_youtube: "YouTube",
  platform_tiktok: "TikTok",
  platform_instagram: "Instagram",
  platform_facebook: "Facebook",
  platform_twitter: "Twitter",
  platform_linkedin: "LinkedIn",
  platform_snapchat: "Snapchat",
  platform_pinterest: "Pinterest",
  platform_wechat: "WeChat",
  platform_weibo: "Weibo",
  platform_douyin: "Douyin",
  platform_xiaohongshu: "Xiaohongshu",

  // Format Descriptions
  format_short_video: "Perfect for short videos",
  format_story: "Ideal for story content",
  format_post: "Great for social media posts",
  format_ad: "Suitable for advertising",
  format_presentation: "Perfect for presentations",
  format_tutorial: "Ideal for tutorials",
  format_intro: "Great for intros",
  format_outro: "Perfect for outros",
  format_logo_animation: "Ideal for logo animations",
  format_product_showcase: "Great for product showcases",
  format_business_intro: "Perfect for business introductions",
  format_course_content: "Ideal for course content",
};

export default enTranslations;
