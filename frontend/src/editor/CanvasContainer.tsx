import { observer } from "mobx-react-lite";
import {
  Box,
  Paper,
  useTheme,
  CircularProgress,
  Typography,
} from "@mui/material";
import { fabric } from "fabric";
import React, { useEffect, useRef, useState, useCallback } from "react";
import { MapInteractionCSS } from "react-map-interaction";
import { StoreContext } from "../store";
import { getUid } from "../utils";

import { normalizeAudioSrc } from "../services/audioService";

import initControl, {
  handleMouseOutCorner,
  handleMouseOverCorner,
} from "./components/controller";
import { initAligningGuidelines, initCenteringGuidelines } from "./guide-lines";

export const CanvasContainer = observer(() => {
  const store = React.useContext(StoreContext);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const boxRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  // 合并相关状态
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(
    null
  );
  const [hasSelectedElement, setHasSelectedElement] = useState(false);

  // 拖拽状态管理
  const [isDragOver, setIsDragOver] = useState(false);

  // 使用store的全局加载状态管理
  const startLoading = useCallback(
    (elementId: string, message: string) => {
      store.startGlobalLoading(elementId, message);
    },
    [store]
  );

  const finishLoading = useCallback(
    (elementId: string) => {
      store.finishGlobalLoading(elementId);
    },
    [store]
  );

  // 统一的媒体元素创建逻辑（与时间线拖拽保持一致）
  const createMediaElement = useCallback((dragData: any, id: string) => {
    const tagName =
      dragData.type === "audio"
        ? "audio"
        : dragData.type === "video"
        ? "video"
        : "img";

    const mediaElement = document.createElement(tagName) as any;
    // 仅对音频进行 Jamendo 代理规范化，其它类型保持原样
    const finalSrc =
      dragData.type === "audio"
        ? normalizeAudioSrc(dragData.src)
        : dragData.src;
    mediaElement.src = finalSrc;
    mediaElement.id = `${dragData.type}-${id}`;
    mediaElement.style.display = "none";

    // 对于图片和GIF，设置跨域属性
    if (["image", "gif"].includes(dragData.type)) {
      mediaElement.crossOrigin = "anonymous";
    }

    // 对于Jamendo音频，设置跨域属性
    if (
      dragData.type === "audio" &&
      (dragData.src.includes("/api/proxy/") ||
        dragData.src.includes("jamendo.com") ||
        dragData.src.includes("jamen.do"))
    ) {
      //mediaElement.crossOrigin = "anonymous";
    }

    document.body.appendChild(mediaElement);
    return mediaElement;
  }, []);

  // 内部的元素后处理逻辑
  const performElementPostProcessing = useCallback(
    (
      element: any,
      x: number,
      y: number,
      duration: number,
      currentTime: number,
      elementType: string
    ) => {
      // 设置初始时间线位置
      element.timeFrame = {
        start: currentTime,
        end: currentTime + duration,
      };

      // 使用TrackManager处理轨道分配和碰撞检测
      const trackResult = store.trackManager.handleNewElement(element);

      // 更新画布位置
      if (element.fabricObject) {
        element.fabricObject.set({
          left: x - element.fabricObject.width! / 2,
          top: y - element.fabricObject.height! / 2,
        });
        element.fabricObject.setCoords();
      }

      // 更新元素并保存
      store.updateEditorElement(element);
      store.canvas?.requestRenderAll();

      // 更新最大时间
      store.updateMaxTime();
    },
    [store]
  );

  // 统一的媒体加载完成处理逻辑（与时间线拖拽保持一致）
  const handleMediaLoadSuccess = useCallback(
    (
      dragData: any,
      element: any,
      mediaElement: any,
      x: number,
      y: number,
      currentTime: number
    ) => {
      const mediaTypeHandlers = {
        image: () => {
          // 对于画布拖拽，直接添加元素而不是替换placeholder
          if (!element.fabricObject) {
            store.addImageElement(mediaElement, element.id, dragData.metadata);
          } else {
            store.replacePlaceholderWithImage(element, mediaElement);
          }
        },
        gif: () => {
          // 对于画布拖拽，直接添加元素而不是替换placeholder
          if (!element.fabricObject) {
            store.addGifElement(mediaElement, element.id, dragData.metadata);
          } else {
            store.replacePlaceholderWithGif(element, mediaElement);
          }
        },
        video: () => {
          // 对于画布拖拽，直接添加元素而不是替换placeholder
          if (!element.fabricObject) {
            store.addVideoElement(mediaElement, element.id, dragData.metadata);
          } else {
            store.replacePlaceholderWithVideo(element, mediaElement);
          }
        },
        audio: () => {
          // 对于画布拖拽，直接添加元素而不是替换placeholder
          if (!element.fabricObject) {
            store.addAudioElement(mediaElement, element.id, dragData.metadata);
          } else {
            store.replacePlaceholderWithAudio(element, mediaElement);
          }
        },
      };

      const handler =
        mediaTypeHandlers[dragData.type as keyof typeof mediaTypeHandlers];
      if (handler) {
        handler();
      }

      // 等待元素被添加到store后进行后处理
      setTimeout(() => {
        const updatedElement = store.editorElements.find(
          (el) => el.id === element.id
        );
        if (updatedElement) {
          const duration = ["video", "audio"].includes(dragData.type)
            ? mediaElement.duration * 1000
            : 3000; // 图片和GIF使用默认3秒

          // 调用后处理逻辑
          performElementPostProcessing(
            updatedElement,
            x,
            y,
            duration,
            currentTime,
            dragData.type
          );
        }

        // 完成加载状态
        finishLoading(element.id);
      }, 100);
    },
    [store, finishLoading, performElementPostProcessing]
  );

  // 统一的异步媒体加载处理（与时间线拖拽保持一致）
  const setupAsyncMediaLoading = useCallback(
    (
      dragData: any,
      element: any,
      id: string,
      x: number,
      y: number,
      currentTime: number
    ) => {
      const mediaElement = createMediaElement(dragData, id);

      // 设置加载成功回调
      const eventType = ["video", "audio"].includes(dragData.type)
        ? "onloadedmetadata"
        : "onload";
      mediaElement[eventType] = () => {
        handleMediaLoadSuccess(
          dragData,
          element,
          mediaElement,
          x,
          y,
          currentTime
        );
      };

      // 设置加载失败回调
      mediaElement.onerror = () => {
        console.error(`Failed to load dropped ${dragData.type}`);
        store.removeEditorElement(element.id);
        finishLoading(element.id);
      };
    },
    [createMediaElement, handleMediaLoadSuccess, store, finishLoading]
  );

  // 统一的元素后处理逻辑（时间线处理、画布位置、轨道分配）
  const handleElementPostProcessing = useCallback(
    (
      element: any,
      x: number,
      y: number,
      duration: number,
      currentTime: number,
      elementType: string
    ) => {
      // 设置初始时间线位置
      element.timeFrame = {
        start: currentTime,
        end: currentTime + duration,
      };

      // 使用TrackManager处理轨道分配和碰撞检测（与点击添加相同的逻辑）
      const trackResult = store.trackManager.handleNewElement(element);

      // 更新画布位置
      if (element.fabricObject) {
        element.fabricObject.set({
          left: x - element.fabricObject.width! / 2,
          top: y - element.fabricObject.height! / 2,
        });
        element.fabricObject.setCoords();
      }

      // 更新元素并保存
      store.updateEditorElement(element);
      store.canvas?.requestRenderAll();

      // 更新最大时间（重要：确保时间线长度正确）
      store.updateMaxTime();
    },
    [store]
  );

  // 计算画布缩放比例，考虑时间线高度
  const calculateCanvasScale = () => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const timelineHeight = store.timelineHeight;
    const navbarHeight = 60; // 导航栏高度

    // 可用的画布区域高度
    const availableHeight = windowHeight - timelineHeight - navbarHeight;

    // 计算缩放比例，确保画布不被遮挡
    const scaleX = (windowWidth * 0.8) / 1920; // 增加水平边距
    const scaleY = (availableHeight * 0.8) / 1080; // 根据可用高度计算
    const scale = Math.min(scaleX, scaleY, 1.0); // 限制最大缩放为1.0

    // 计算画布居中位置
    const x = windowWidth / 2;
    const y = availableHeight / 2 + navbarHeight;

    return { scale, translation: { x, y } };
  };

  // 使用Store中的缩放值，提取初始化逻辑为单独函数
  const initializeCanvasScale = () => {
    const { scale, translation } = calculateCanvasScale();
    store.updateCanvasScale(scale, translation);
    return {
      scale: store.canvasScale,
      translation: store.canvasTranslation,
    };
  };

  const [value, setValue] = useState(initializeCanvasScale);

  // 防抖函数
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  // 防抖的缩放调整函数（完整重新计算缩放和平移）
  const debouncedScaleAdjustment = debounce(() => {
    const { scale, translation } = calculateCanvasScale();
    const newValue = { scale, translation };

    // 使用requestAnimationFrame确保平滑更新
    requestAnimationFrame(() => {
      setValue(newValue);
      store.updateCanvasScale(scale, translation);
    });
  }, 30); // 30ms防抖

  // 防抖的平移调整函数（只调整平移位置，保持当前缩放值）
  const debouncedTranslationAdjustment = debounce(() => {
    const { translation } = calculateCanvasScale();
    // 保持当前的缩放值，只更新平移位置
    const newValue = { scale: store.canvasScale, translation };

    // 使用requestAnimationFrame确保平滑更新
    requestAnimationFrame(() => {
      setValue(newValue);
      store.updateCanvasScale(store.canvasScale, translation, false); // 平移调整不保存
    });
  }, 30); // 30ms防抖

  // 监听时间线高度变化，只调整画布平移位置（保持用户设置的缩放值）
  useEffect(() => {
    debouncedTranslationAdjustment();
  }, [store.timelineHeight]); // 依赖时间线高度

  // 监听窗口大小变化，重新计算画布缩放
  useEffect(() => {
    const handleResize = () => {
      debouncedScaleAdjustment();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []); // 移除时间线高度依赖，避免重复监听

  // 优化Canvas初始化
  useEffect(() => {
    if (store?.canvas) return;

    const canvas = new fabric.Canvas("myCanvas", {
      selection: true,
      preserveObjectStacking: true,
      selectionColor: "rgba(59, 130, 246, 0.08)",
      selectionBorderColor: "rgba(59, 130, 246, 0.6)",
      enableRetinaScaling: true,
      fireRightClick: false,
      controlsAboveOverlay: true,
      imageSmoothingEnabled: true,
      width: 1920,
      height: 1080,
    });

    // 设置Canvas属性
    const setCanvasProperties = () => {
      fabric.Object.prototype.cornerSize = 20;
      fabric.Object.prototype.borderColor = "rgba(59, 130, 246, 0.6)";
      fabric.Object.prototype.cornerColor = "white";
      fabric.Object.prototype.cornerStrokeColor = "rgba(59, 130, 246, 0.4)";
      fabric.Object.prototype.borderOpacityWhenMoving = 0.8;
      fabric.Object.prototype.centeredScaling = false;
      fabric.Object.prototype.centeredRotation = true;
      fabric.Object.prototype.transparentCorners = false;
    };

    // 添加全局文本编辑模式监听器，防止影响页面高度
    const setupTextEditingFix = () => {
      // 监听DOM变化，捕获Fabric.js创建的textarea
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // 检查是否是Fabric.js创建的编辑textarea
              if (
                element.tagName === "TEXTAREA" &&
                element.getAttribute("style")?.includes("position: absolute")
              ) {
                fixTextareaStyles(element as HTMLTextAreaElement);
              }
              // 也检查子元素中的textarea
              const textareas = element.querySelectorAll(
                'textarea[style*="position: absolute"]'
              );
              textareas.forEach((textarea) => {
                fixTextareaStyles(textarea as HTMLTextAreaElement);
              });
            }
          });
        });
      });

      // 开始观察DOM变化
      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      // 返回清理函数
      return () => observer.disconnect();
    };

    // 修复textarea样式的函数
    const fixTextareaStyles = (textarea: HTMLTextAreaElement) => {
      // 将绝对定位改为固定定位，防止影响页面布局
      textarea.style.position = "fixed";
      textarea.style.zIndex = "9999";
    };

    setCanvasProperties();

    // 设置文本编辑模式修复
    const cleanupTextEditingFix = setupTextEditingFix();

    // @ts-ignore
    canvas.store = store;
    store.setCanvas(canvas);

    initControl();
    initAligningGuidelines(canvas);
    initCenteringGuidelines(canvas);

    // 初始化事件监听
    canvas.on("mouse:up", handleMouseUp);
    canvas.on("mouse:over", handleMouseOver);
    canvas.on("mouse:out", handleMouseOut);
    canvas.on("mouse:down", handleMouseDown);
    canvas.on("selection:created", handleSelection);
    canvas.on("selection:updated", handleSelection);
    canvas.on("selection:cleared", handleSelectionCleared);

    if (boxRef.current) {
      canvas.setWidth(boxRef.current.offsetWidth);
      canvas.setHeight(boxRef.current.offsetHeight);
    }

    canvas.requestRenderAll();
    store.loadFromLocalStorage();

    return () => {
      if (store.canvas) {
        canvas.off("mouse:up", handleMouseUp);
        canvas.off("mouse:over", handleMouseOver);
        canvas.off("mouse:out", handleMouseOut);
        canvas.off("mouse:down", handleMouseDown);
        canvas.off("selection:created", handleSelection);
        canvas.off("selection:updated", handleSelection);
        canvas.off("selection:cleared", handleSelectionCleared);
        store.destroy();
      }
      // 清理文本编辑模式监听器
      cleanupTextEditingFix();
    };
  }, [theme]);

  // 简化事件处理函数
  const handleSelection = (e: fabric.IEvent) => {
    const selected = e.selected?.[0];
    setSelectedObject(selected || null);
    setHasSelectedElement(!!selected);

    // 检查是否选中了字幕对象
    if (selected && store.captionManager) {
      const captionTextObject = (store.captionManager as any).captionTextObject;
      if (selected === captionTextObject) {
        // 如果选中的是字幕对象，找到对应的字幕并选中
        const currentCaption = store.captionManager.findCurrentCaption(
          store.currentTimeInMs
        );
        if (currentCaption && !currentCaption.isSelected) {
          store.selectCaption(currentCaption.id);
        }
      } else {
        // 如果选中的是其他对象（非字幕），同步store的选中状态
        const element = store.editorElements.find(
          (el) => el.fabricObject === selected
        );
        if (element && store.selectedElement?.id !== element.id) {
          // 只有当选中的元素与store中的不同时才更新，避免循环调用
          store.selectedElement = element;
          store.captionManager.deselectAllCaptions();
        }
      }
    } else if (!selected) {
      // 如果没有选中任何对象，清除store的所有选中状态
      store.clearAllSelections();
    }
  };

  const handleSelectionCleared = () => {
    setSelectedObject(null);
    setHasSelectedElement(false);
  };

  const handleMouseUp = () => {};

  const handleMouseOver = (e: fabric.IEvent) => {
    const corner = e.target ? (e.target as any).__corner : undefined;
    if (corner) {
      handleMouseOverCorner(corner, e.target);
    }
    store.canvas.renderAll();
  };

  const handleMouseOut = (e: fabric.IEvent) => {
    if (e.target) {
      e.target.set({ borderColor: null });
      handleMouseOutCorner(e.target);
      store.canvas.requestRenderAll();
    }
  };

  const handleMouseDown = (opt: fabric.IEvent) => {
    if (store.editMode === "move") {
      if (!opt.target) {
        // 点击画布空白区域，清除所有选择状态
        store.clearAllSelections();
        setHasSelectedElement(false);
      } else {
        setHasSelectedElement(true);
      }
    } else {
      store.canvas.discardActiveObject();
      store.clearAllSelections();
      setHasSelectedElement(false);
      store.canvas.requestRenderAll();
    }
  };

  // 拖拽事件处理函数
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";

      if (!isDragOver) {
        setIsDragOver(true);
      }
    },
    [isDragOver]
  );

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    // 只有当离开整个画布容器时才清除拖拽状态
    const rect = boxRef.current?.getBoundingClientRect();
    if (rect) {
      const { clientX, clientY } = e;
      const isOutside =
        clientX < rect.left ||
        clientX > rect.right ||
        clientY < rect.top ||
        clientY > rect.bottom;

      if (isOutside) {
        setIsDragOver(false);
      }
    }
  }, []);

  const handleDrop = useCallback(
    async (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        const dragData = JSON.parse(e.dataTransfer.getData("application/json"));

        // 计算画布坐标
        const rect = boxRef.current?.getBoundingClientRect();
        if (!rect || !store.canvas) {
          return;
        }

        // 获取画布中心点
        const canvasRect = store.canvas.getElement().getBoundingClientRect();
        const canvasCenterX = canvasRect.left + canvasRect.width / 2;
        const canvasCenterY = canvasRect.top + canvasRect.height / 2;

        // 计算相对于画布中心的偏移
        const offsetX = e.clientX - canvasCenterX;
        const offsetY = e.clientY - canvasCenterY;

        // 转换为画布坐标（考虑缩放）
        const canvasX = store.canvasWidth / 2 + offsetX / value.scale;
        const canvasY = store.canvasHeight / 2 + offsetY / value.scale;

        // 根据元素类型处理拖拽（异步处理，不阻塞UI）
        handleElementDrop(dragData, canvasX, canvasY).catch((error) => {
          console.error("Error handling canvas drop:", error);
        });
      } catch (error) {
        console.error("Error handling canvas drop:", error);
      }
    },
    [store, value.scale]
  );

  // 处理不同类型元素的拖拽
  const handleElementDrop = useCallback(
    async (dragData: any, x: number, y: number) => {
      const currentTime = store.currentTimeInMs;

      switch (dragData.type) {
        case "image":
          await handleImageDrop(dragData, x, y, currentTime);
          break;
        case "video":
          await handleVideoDrop(dragData, x, y, currentTime);
          break;
        case "text":
          await handleTextDrop(dragData, x, y, currentTime);
          break;
        case "audio":
          await handleAudioDrop(dragData, x, y, currentTime);
          break;
        case "shape":
          await handleShapeDrop(dragData, x, y, currentTime);
          break;
        case "gif":
          await handleGifDrop(dragData, x, y, currentTime);
          break;
        default:
          console.warn("Unknown drag data type:", dragData.type);
      }
    },
    [store, handleElementPostProcessing]
  );

  // 处理图片拖拽
  const handleImageDrop = useCallback(
    async (dragData: any, x: number, y: number, currentTime: number) => {
      const elementId = getUid();

      // 立即显示加载状态
      startLoading(elementId, "正在加载图片...");

      try {
        // 创建一个临时元素对象用于媒体加载
        const tempElement = { id: elementId, fabricObject: null };

        // 使用统一的异步媒体加载处理
        setupAsyncMediaLoading(
          dragData,
          tempElement,
          elementId,
          x,
          y,
          currentTime
        );
      } catch (error) {
        console.error("Error handling image drop:", error);
        finishLoading(elementId);
      }
    },
    [startLoading, finishLoading, setupAsyncMediaLoading]
  );

  // 处理视频拖拽
  const handleVideoDrop = useCallback(
    async (dragData: any, x: number, y: number, currentTime: number) => {
      const elementId = getUid();

      // 立即显示加载状态
      startLoading(elementId, "正在加载视频...");

      try {
        // 创建一个临时元素对象用于媒体加载
        const tempElement = { id: elementId, fabricObject: null };

        // 使用统一的异步媒体加载处理
        setupAsyncMediaLoading(
          dragData,
          tempElement,
          elementId,
          x,
          y,
          currentTime
        );
      } catch (error) {
        console.error("Error handling video drop:", error);
        finishLoading(elementId);
      }
    },
    [startLoading, finishLoading, setupAsyncMediaLoading]
  );

  // 处理文字拖拽
  const handleTextDrop = useCallback(
    async (dragData: any, x: number, y: number, currentTime: number) => {
      try {
        const { textData } = dragData;
        const elementId = getUid();

        // 添加文字元素到store
        store.addText({
          ...textData,
          id: elementId,
        });

        // 等待元素被添加到store
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 获取新添加的元素并进行统一后处理
        const element = store.editorElements.find((el) => el.id === elementId);
        if (element) {
          handleElementPostProcessing(element, x, y, 5000, currentTime, "文字");
        }
      } catch (error) {
        console.error("Error handling text drop:", error);
      }
    },
    [store, handleElementPostProcessing]
  );

  // 处理音频拖拽
  const handleAudioDrop = useCallback(
    async (dragData: any, _x: number, _y: number, currentTime: number) => {
      try {
        startLoading("audio-drop", "正在加载音频...");

        const { src, metadata } = dragData;
        const elementId = getUid();

        // 创建音频元素
        const audioElement = document.createElement("audio");
        const finalSrc = normalizeAudioSrc(src);
        audioElement.src = finalSrc;
        audioElement.id = `audio-${elementId}`;
        audioElement.style.display = "none";

        // 对于Jamendo音频，设置跨域属性（代理后不强制crossOrigin）
        if (
          finalSrc.includes("/api/proxy/") ||
          finalSrc.includes("jamendo.com") ||
          finalSrc.includes("jamen.do")
        ) {
          // audioElement.crossOrigin = "anonymous";
        }

        document.body.appendChild(audioElement);

        // 等待音频元数据加载
        await new Promise((resolve, reject) => {
          audioElement.addEventListener("loadedmetadata", resolve);
          audioElement.addEventListener("error", reject);
        });

        // 添加音频元素到store
        store.addAudioElement(audioElement, elementId, metadata);

        // 等待元素被添加到store
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 获取新添加的元素并进行统一后处理
        const element = store.editorElements.find((el) => el.id === elementId);
        if (element) {
          const duration = audioElement.duration * 1000; // 使用音频实际时长
          handleElementPostProcessing(
            element,
            0,
            0,
            duration,
            currentTime,
            "音频"
          );
        }
      } catch (error) {
        console.error("Error handling audio drop:", error);
      } finally {
        finishLoading("audio-drop");
      }
    },
    [store, startLoading, finishLoading, handleElementPostProcessing]
  );

  // 处理形状拖拽
  const handleShapeDrop = useCallback(
    async (dragData: any, x: number, y: number, currentTime: number) => {
      try {
        const { shapeType } = dragData;
        const elementId = getUid();

        // 添加形状元素到store
        store.addShapeElement(shapeType, { id: elementId });

        // 等待元素被添加到store
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 获取新添加的元素并进行统一后处理
        const element = store.editorElements.find((el) => el.id === elementId);
        if (element) {
          handleElementPostProcessing(element, x, y, 5000, currentTime, "形状");
        }
      } catch (error) {
        console.error("Error handling shape drop:", error);
      }
    },
    [store, handleElementPostProcessing]
  );

  // 处理GIF拖拽
  const handleGifDrop = useCallback(
    async (dragData: any, x: number, y: number, currentTime: number) => {
      const elementId = getUid();

      // 立即显示加载状态
      startLoading(elementId, "正在加载GIF...");

      try {
        // 创建一个临时元素对象用于媒体加载
        const tempElement = { id: elementId, fabricObject: null };

        // 使用统一的异步媒体加载处理
        setupAsyncMediaLoading(
          dragData,
          tempElement,
          elementId,
          x,
          y,
          currentTime
        );
      } catch (error) {
        console.error("Error handling gif drop:", error);
        finishLoading(elementId);
      }
    },
    [startLoading, finishLoading, setupAsyncMediaLoading]
  );

  // 优化编辑模式变化处理
  useEffect(() => {
    if (!store.canvas) return;

    const isHandMode = store.editMode === "hand";
    store.canvas.selection = !isHandMode;

    if (isHandMode) {
      store.canvas.discardActiveObject();
      store.setSelectedElement(null);
      store.deselectAllCaptions();
      setHasSelectedElement(false);
    }

    // 批量设置对象属性
    store.canvas.getObjects().forEach((obj) => {
      obj.selectable = !isHandMode;
      obj.evented = !isHandMode;
    });

    store.canvas.requestRenderAll();
  }, [store.editMode]);

  // 监听画布缩放变化事件
  useEffect(() => {
    const handleCanvasZoomChange = (event: CustomEvent) => {
      setValue(event.detail);
    };

    window.addEventListener(
      "canvas-zoom-change",
      handleCanvasZoomChange as EventListener
    );
    return () => {
      window.removeEventListener(
        "canvas-zoom-change",
        handleCanvasZoomChange as EventListener
      );
    };
  }, []);

  // 监听字幕选中状态变化，确保canvas上的字幕元素也被选中
  useEffect(() => {
    if (!store.canvas || !store.captionManager) return;

    const selectedCaption = store.getSelectedCaption();
    if (selectedCaption) {
      // 如果有选中的字幕，但canvas上的字幕对象未被选中，则选中它
      const captionTextObject = (store.captionManager as any).captionTextObject;
      if (
        captionTextObject &&
        store.canvas.getActiveObject() !== captionTextObject
      ) {
        store.canvas.setActiveObject(captionTextObject);
        store.canvas.requestRenderAll();
      }
    }
  }, [store.captions]); // 监听字幕数组的变化

  // 监听store中selectedElement的变化，确保canvas的选中状态同步
  useEffect(() => {
    if (!store.canvas) return;

    const currentActiveObject = store.canvas.getActiveObject();

    if (store.selectedElement?.fabricObject) {
      // 如果store中有选中的元素，但canvas中的活动对象不是这个元素，则同步
      if (currentActiveObject !== store.selectedElement.fabricObject) {
        store.canvas.setActiveObject(store.selectedElement.fabricObject);
        store.canvas.requestRenderAll();
      }
    } else if (currentActiveObject && !store.getSelectedCaption()) {
      // 如果store中没有选中的元素，且没有选中的字幕，但canvas中有活动对象，则清除
      store.canvas.discardActiveObject();
      store.canvas.requestRenderAll();
    }
  }, [store.selectedElement]); // 监听selectedElement的变化

  // 优化渲染部分
  return (
    <Paper
      elevation={3}
      sx={{
        width: "100%",
        height: `100%`, // 动态计算高度
        overflow: "hidden",
        position: "relative",
      }}
    >
      <Box
        id="grid-canvas-container"
        className="canvas-container"
        ref={boxRef}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        sx={(theme) => ({
          width: "100%",
          height: "100%",
          backgroundImage: `radial-gradient(circle at 15px 15px, ${theme.palette.grey[100]} 2px, transparent 0)`,
          backgroundSize: "35px 35px",
          backgroundPosition: "center center",
          // 拖拽状态下的视觉反馈
          ...(isDragOver && {
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            border: "2px dashed rgba(59, 130, 246, 0.5)",
          }),
        })}
      >
        <MapInteractionCSS
          onChange={(newValue: any) => {
            setValue(newValue);
            store.updateCanvasScale(newValue.scale, newValue.translation);
          }}
          value={value}
          disablePan={store.editMode === "move"}
          disableZoom={store.editMode === "move"}
          defaultScale={store.canvasScale}
          minScale={0.1}
          maxScale={2}
          style={{
            width: "100%",
            height: "100%",
            position: "relative",
          }}
        >
          <Box
            sx={(theme) => ({
              position: "absolute",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              boxShadow: 3,
              borderRadius: 1,
              overflow: "hidden",
              border: `2px solid ${
                theme.palette.mode === "dark"
                  ? theme.palette.grey[700]
                  : theme.palette.grey[300]
              }`,
            })}
          >
            <canvas id="myCanvas" ref={canvasRef} />
          </Box>
        </MapInteractionCSS>
      </Box>

      {/* 画布中心加载状态指示器 */}
      {store.hasGlobalLoading && (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: "rgba(0, 0, 0, 0.85)",
            color: "white",
            padding: "20px 32px",
            borderRadius: "12px",
            zIndex: 1000,
            pointerEvents: "none",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
            backdropFilter: "blur(8px)",
            boxShadow: "0 8px 32px rgba(0, 0, 0, 0.4)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
            minWidth: "200px",
          }}
        >
          <CircularProgress
            size={32}
            thickness={3}
            sx={{
              color: "white",
              "& .MuiCircularProgress-circle": {
                strokeLinecap: "round",
              },
            }}
          />
          <Typography
            variant="body2"
            sx={{
              color: "white",
              fontWeight: 500,
              textAlign: "center",
              lineHeight: 1.4,
              maxWidth: "180px",
              wordBreak: "break-word",
            }}
          >
            {store.globalLoadingMessage}
          </Typography>
        </Box>
      )}
    </Paper>
  );
});
