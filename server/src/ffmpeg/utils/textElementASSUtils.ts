import { Caption, CaptionStyle } from "../types";
import { ASSSubtitleUtils } from "./assSubtitleUtils";
import { BaseASSUtils, COMMON_ASS_CONSTANTS } from "./baseASSUtils";

/**
 * 文字布局信息接口
 */
interface TextLayoutInfo {
  // 背景框信息
  backgroundX: number;
  backgroundY: number;
  backgroundWidth: number;
  backgroundHeight: number;

  // 文字位置信息（在背景框内的位置）
  textX: number;
  textY: number;
  textAlign: "left" | "center" | "right";
}

/**
 * ASS样式标签构建器接口
 */
interface ASSStyleTags {
  position: string;
  fontSize: string;
  fontStyle: string;
  color: string;
  stroke: string;
  charSpacing: string;
  shadow: string;
  opacity: string;
  lineHeight: string;
}

/**
 * 文字元素专用配置常量（继承公共常量，添加专用配置）
 */
const CONFIG = {
  // 默认值
  DEFAULT_BACKGROUND_WIDTH: 200,
  DEFAULT_BACKGROUND_HEIGHT: 50,
  DEFAULT_TEXT_ALIGN: "center" as const,

  // 内边距
  PADDING_X: 0,
  PADDING_Y: 0,
  TEXT_MARGIN_X: 10,

  // 字符宽度比例
  CHINESE_CHAR_WIDTH_RATIO: 0.9,
  ENGLISH_CHAR_WIDTH_RATIO: 0.6,

  // 字体大小调整限制
  FONT_SIZE_MIN_RATIO: 0.3,
  FONT_SIZE_MAX_RATIO: 1.2,
  FONT_SIZE_ADJUSTMENT_THRESHOLD: 2,

  // 从公共常量继承
  ...COMMON_ASS_CONSTANTS,
} as const;

/**
 * 专门用于文字元素的ASS字幕工具类
 * 提供更精确的位置控制，专门为文字元素设计
 *
 * 主要功能：
 * - 创建文字元素的ASS字幕文件
 * - 支持背景色、渐变色、阴影、描边等样式
 * - 自动调整字体大小以适应背景框
 * - 支持多行文本和自定义行高
 * - 提供精确的位置控制
 *
 * <AUTHOR>
 * @version 2.0
 */
export class TextElementASSUtils {
  /**
   * 为文字元素创建ASS字幕文件
   * 使用绝对位置定位，确保与前端显示完全一致
   * 支持动画效果，确保与前端动画效果一致
   */
  static createTextElementASSFile(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    elementOpacity?: number,
    animationInfo?: any
  ): string {
    this.validateInputs(caption, style, canvasWidth, canvasHeight);

    try {
      const assContent = this.generateTextElementASSContent(
        caption,
        style,
        canvasWidth,
        canvasHeight,
        elementOpacity,
        animationInfo
      );

      const subtitlePath = this.createTempFile(caption.id, assContent);
      console.log(`为文字元素 ${caption.id} 创建ASS字幕文件: ${subtitlePath}`);

      if (animationInfo) {
        console.log(`文字元素 ${caption.id} 包含动画效果:`, animationInfo);
      }

      return subtitlePath;
    } catch (error) {
      const errorMessage = `创建文字元素ASS文件失败: ${
        error instanceof Error ? error.message : String(error)
      }`;
      console.error(errorMessage, error);
      throw new Error(errorMessage);
    }
  }

  /**
   * 验证输入参数
   */
  private static validateInputs(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): void {
    if (!caption?.id) {
      throw new Error("Caption ID is required");
    }
    if (!caption.text?.trim()) {
      throw new Error("Caption text cannot be empty");
    }
    if (canvasWidth <= 0 || canvasHeight <= 0) {
      throw new Error("Canvas dimensions must be positive");
    }
    if (caption.startTime >= caption.endTime) {
      throw new Error("Invalid time range: start time must be before end time");
    }
  }

  /**
   * 创建临时文件
   */
  private static createTempFile(captionId: string, content: string): string {
    return BaseASSUtils.createTempFile("text_element", content, captionId);
  }

  /**
   * 生成文字元素的ASS文件内容
   * 支持动画效果
   */
  private static generateTextElementASSContent(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    elementOpacity?: number,
    animationInfo?: any
  ): string {
    const parts: string[] = [];

    // 生成文件头部和样式
    parts.push(
      this.generateASSHeader(canvasWidth, canvasHeight),
      `Style: Default,${ASSSubtitleUtils.generateASSStyle(
        style,
        canvasWidth,
        canvasHeight
      )}\n`,
      `[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`
    );

    // 处理时间和布局
    const timeInfo = this.getTimeInfo(caption);
    const layoutInfo = this.calculateTextLayout(
      caption,
      style,
      canvasWidth,
      canvasHeight
    );

    // 添加背景和文字（包含动画效果）
    this.addDialogueLinesWithAnimation(
      parts,
      timeInfo,
      layoutInfo,
      caption,
      style,
      elementOpacity,
      animationInfo
    );

    return parts.join("");
  }

  /**
   * 获取时间信息
   */
  private static getTimeInfo(caption: Caption) {
    return {
      startTime: BaseASSUtils.convertTimeToASS(caption.startTime),
      endTime: BaseASSUtils.convertTimeToASS(caption.endTime),
    };
  }

  /**
   * 添加对话行
   */
  private static addDialogueLines(
    parts: string[],
    timeInfo: { startTime: string; endTime: string },
    layoutInfo: TextLayoutInfo,
    caption: Caption,
    style: CaptionStyle,
    elementOpacity?: number
  ): void {
    const { startTime, endTime } = timeInfo;

    // 背景层
    if (this.shouldRenderBackground(style)) {
      const backgroundRect = this.generateBackgroundRectangle(
        layoutInfo,
        style
      );
      parts.push(
        `Dialogue: ${CONFIG.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${backgroundRect}\n`
      );
    }

    // 文字层
    const textContent = this.generateTextContent(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );
    parts.push(
      `Dialogue: ${CONFIG.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${textContent}\n`
    );
  }

  /**
   * 添加带动画效果的对话行
   * 支持淡入淡出、滑动、擦除等动画效果
   */
  private static addDialogueLinesWithAnimation(
    parts: string[],
    timeInfo: { startTime: string; endTime: string },
    layoutInfo: TextLayoutInfo,
    caption: Caption,
    style: CaptionStyle,
    elementOpacity?: number,
    animationInfo?: any
  ): void {
    const { startTime, endTime } = timeInfo;

    // 如果没有动画信息，使用原来的方法
    if (!animationInfo) {
      this.addDialogueLines(
        parts,
        timeInfo,
        layoutInfo,
        caption,
        style,
        elementOpacity
      );
      return;
    }

    // 背景层（如果需要）
    if (this.shouldRenderBackground(style)) {
      const backgroundRect = this.generateBackgroundRectangle(
        layoutInfo,
        style
      );

      // 为背景添加动画效果，确保背景框跟随文字移动
      const animatedBackgroundRect = this.applyAnimationToBackground(
        backgroundRect,
        animationInfo,
        layoutInfo,
        timeInfo
      );

      parts.push(
        `Dialogue: ${CONFIG.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${animatedBackgroundRect}\n`
      );
    }

    // 文字层（带动画效果）
    const textContent = this.generateTextContent(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );

    // 为文字添加动画效果
    const animatedTextContent = this.applyAnimationToContent(
      textContent,
      animationInfo,
      layoutInfo,
      timeInfo
    );

    parts.push(
      `Dialogue: ${CONFIG.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${animatedTextContent}\n`
    );
  }

  /**
   * 判断是否应该渲染背景
   */
  private static shouldRenderBackground(style: CaptionStyle): boolean {
    return !!(style.backgroundColor && style.backgroundColor !== "transparent");
  }

  /**
   * 生成文字内容（不包含背景）
   */
  private static generateTextContent(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle,
    elementOpacity?: number
  ): string {
    // 首先进行自动换行处理
    let processedText = this.wrapTextToWidth(
      caption.text,
      layoutInfo.backgroundWidth,
      style
    );

    let escapedText = BaseASSUtils.escapeASSText(processedText);

    // 构建样式标签
    const styleTags = this.buildStyleTags(
      caption,
      layoutInfo,
      style,
      elementOpacity
    );

    // 处理多行文本的行高
    escapedText = this.processMultilineText(escapedText, style);

    // 组合所有标签
    return this.combineStyleTags(styleTags) + escapedText;
  }

  /**
   * 构建所有样式标签
   */
  private static buildStyleTags(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle,
    elementOpacity?: number
  ): ASSStyleTags {
    const adjustedFontSize = this.calculateAdjustedFontSize(
      caption,
      layoutInfo,
      style
    );

    return {
      position: this.generateTextPositionTags(layoutInfo, style),
      fontSize:
        adjustedFontSize !== style.fontSize ? `{\\fs${adjustedFontSize}}` : "",
      fontStyle: this.generateFontStyleTags(style),
      color: this.generateColorTags(style),
      stroke: this.generateStrokeTags(style),
      charSpacing: this.generateCharSpacingTags(style),
      shadow: this.generateShadowTags(style),
      opacity: this.generateOpacityTags(style, elementOpacity),
      lineHeight: this.generateLineHeightTags(style),
    };
  }

  /**
   * 组合样式标签
   */
  private static combineStyleTags(tags: ASSStyleTags): string {
    return Object.values(tags).join("");
  }

  /**
   * 根据宽度限制自动换行文字
   * 模拟fabric.Textbox的自动换行行为
   */
  private static wrapTextToWidth(
    text: string,
    maxWidth: number,
    style: CaptionStyle
  ): string {
    if (!text || maxWidth <= 0) {
      return text;
    }

    // 如果文本已经包含手动换行符，先按换行符分割
    const paragraphs = text.split("\n");
    const wrappedParagraphs: string[] = [];

    for (const paragraph of paragraphs) {
      if (paragraph.trim() === "") {
        wrappedParagraphs.push("");
        continue;
      }

      // 对每个段落进行自动换行
      const wrappedLines = this.wrapParagraphToWidth(
        paragraph,
        maxWidth,
        style
      );
      wrappedParagraphs.push(wrappedLines);
    }

    return wrappedParagraphs.join("\n");
  }

  /**
   * 估算整段文本的像素宽度（更精确，用于判断是否需要换行）
   */
  private static estimateTextWidthPx(
    text: string,
    fontSize: number,
    fontFamily?: string,
    charSpacing: number = 0
  ): number {
    let width = 0;
    for (let i = 0; i < text.length; i++) {
      const ch = text[i];
      width += this.estimateCharPixelWidth(ch, fontSize, fontFamily);
      if (i > 0) width += charSpacing;
    }
    return width;
  }

  /**
   * 估算单个字符的像素宽度（区分中英文、空格、标点）
   */
  private static estimateCharPixelWidth(
    ch: string,
    fontSize: number,
    fontFamily?: string
  ): number {
    const isMono = fontFamily && fontFamily.toLowerCase().includes("mono");
    if (ch === " ") return fontSize * (isMono ? 0.5 : 0.33);
    // CJK字符近似等宽
    if (this.isCJKCharacter(ch)) return fontSize * (isMono ? 0.95 : 1.0);
    // 常见英文/数字字符
    if (/^[A-Za-z0-9]$/.test(ch)) return fontSize * (isMono ? 0.6 : 0.55);
    // 其他标点与符号，取中间值
    return fontSize * (isMono ? 0.55 : 0.5);
  }

  /**
   * 判断一段文本在给定宽度内是否可单行放下
   */
  private static fitsInWidth(
    paragraph: string,
    maxWidth: number,
    style: CaptionStyle
  ): boolean {
    const fontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;
    const charSpacing = style.charSpacing || 0;
    const padding = CONFIG.TEXT_MARGIN_X * 2; // 与渲染背景一致
    const availableWidth = Math.max(0, maxWidth - padding); // 不减安全边距用于更宽松判断
    const textWidth = this.estimateTextWidthPx(
      paragraph,
      fontSize,
      style.fontFamily,
      charSpacing
    );
    return textWidth <= availableWidth;
  }

  /**
   * 对单个段落进行换行处理
   */
  private static wrapParagraphToWidth(
    paragraph: string,
    maxWidth: number,
    style: CaptionStyle
  ): string {
    const fontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;
    const charSpacing = style.charSpacing || 0;

    // 计算可用宽度（减去内边距和安全边距）
    const padding = CONFIG.TEXT_MARGIN_X * 2;
    const safetyMargin = fontSize * 0.2; // 添加安全边距，防止超出边界
    const availableWidth = maxWidth - padding - safetyMargin;

    if (availableWidth <= 0) {
      return paragraph;
    }

    // 如果整段文本在宽度内，则不换行，直接返回
    if (this.fitsInWidth(paragraph, maxWidth, style)) {
      return paragraph;
    }

    // 估算每个字符的平均宽度
    const avgCharWidth = this.estimateCharWidth(fontSize, style.fontFamily);

    // 考虑字符间距
    const effectiveCharWidth = avgCharWidth + charSpacing;

    // 估算每行可容纳的字符数，使用更保守的计算
    const maxCharsPerLine = Math.floor(availableWidth / effectiveCharWidth);

    if (maxCharsPerLine <= 0) {
      return paragraph;
    }

    // 按单词和字符进行智能换行
    return this.intelligentWrap(paragraph, maxCharsPerLine);
  }

  /**
   * 智能换行：优先在单词边界换行，中文字符可以任意位置换行
   * 使用更精确的宽度计算，考虑中英文字符宽度差异
   */
  private static intelligentWrap(
    text: string,
    maxCharsPerLine: number
  ): string {
    if (text.length <= maxCharsPerLine) {
      return text;
    }

    const lines: string[] = [];
    let currentLine = "";
    let currentWidth = 0;
    let i = 0;

    while (i < text.length) {
      const char = text[i];

      // 估算当前字符的宽度（中文字符通常比英文字符宽）
      const charWidth = this.isCJKCharacter(char) ? 1.5 : 1;

      // 检查添加当前字符后是否超出限制
      if (currentWidth + charWidth > maxCharsPerLine) {
        // 尝试在单词边界换行（仅对英文）
        const lastSpaceIndex = currentLine.lastIndexOf(" ");

        if (lastSpaceIndex > 0 && this.isEnglishText(currentLine)) {
          // 在最后一个空格处换行
          lines.push(currentLine.substring(0, lastSpaceIndex));
          currentLine = currentLine.substring(lastSpaceIndex + 1) + char;
          // 重新计算当前行宽度
          currentWidth = this.calculateLineWidth(currentLine);
        } else {
          // 直接换行
          lines.push(currentLine);
          currentLine = char;
          currentWidth = charWidth;
        }
      } else {
        currentLine += char;
        currentWidth += charWidth;
      }

      i++;
    }

    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.join("\n");
  }

  /**
   * 检查字符是否为中日韩字符
   */
  private static isCJKCharacter(char: string): boolean {
    const code = char.charCodeAt(0);
    return (
      (code >= 0x4e00 && code <= 0x9fff) || // 中文
      (code >= 0x3400 && code <= 0x4dbf) || // 中文扩展A
      (code >= 0x3040 && code <= 0x309f) || // 日文平假名
      (code >= 0x30a0 && code <= 0x30ff) || // 日文片假名
      (code >= 0xac00 && code <= 0xd7af) // 韩文
    );
  }

  /**
   * 计算行的实际宽度（考虑中英文字符差异）
   */
  private static calculateLineWidth(line: string): number {
    let width = 0;
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      width += this.isCJKCharacter(char) ? 1.5 : 1;
    }
    return width;
  }

  /**
   * 估算字符宽度
   * 更精确的字符宽度估算，考虑不同字体和字符类型
   */
  private static estimateCharWidth(
    fontSize: number,
    fontFamily?: string
  ): number {
    // 根据字体类型调整宽度比例
    const isMonospace = fontFamily && fontFamily.toLowerCase().includes("mono");

    if (isMonospace) {
      return fontSize * 0.6; // 等宽字体
    }

    // 对于比例字体，使用更保守的估算
    // 考虑到中文字符通常比英文字符宽，使用更大的系数
    return fontSize * 0.7; // 增加系数以确保不超出边界
  }

  /**
   * 检查文本是否主要是英文
   */
  private static isEnglishText(text: string): boolean {
    const englishChars = text.match(/[a-zA-Z\s]/g);
    return englishChars ? englishChars.length / text.length > 0.7 : false;
  }

  /**
   * 处理多行文本
   */
  private static processMultilineText(
    escapedText: string,
    style: CaptionStyle
  ): string {
    if (
      style.lineHeight &&
      style.lineHeight !== 1 &&
      escapedText.includes("\\N")
    ) {
      const lineSpacing = Math.round(
        (style.lineHeight - 1) *
          (style.fontSize || CONFIG.DEFAULT_FONT_SIZE) *
          CONFIG.LINE_SPACING_MULTIPLIER
      );

      if (lineSpacing > 0) {
        return escapedText.replace(/\\N/g, `\\N{\\fsp${lineSpacing}}`);
      }
    }
    return escapedText;
  }

  /**
   * 计算文字布局信息
   * 包括背景框位置和文字在框内的位置
   */
  private static calculateTextLayout(
    caption: Caption,
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number
  ): TextLayoutInfo {
    // 使用默认值或计算背景框尺寸
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(caption, style);

    // 获取位置信息
    const backgroundX = style.positionX ?? 0;
    const backgroundY = style.positionY ?? 0;

    // 计算文字位置
    const { textX, textY } = this.calculateTextPosition(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style.textAlign || CONFIG.DEFAULT_TEXT_ALIGN,
      style
    );

    return {
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      textX,
      textY,
      textAlign: style.textAlign || CONFIG.DEFAULT_TEXT_ALIGN,
    };
  }

  /**
   * 计算背景框尺寸
   * 确保背景高度不小于多行文本的所需高度
   */
  private static calculateBackgroundDimensions(
    caption: Caption,
    style: CaptionStyle
  ): { backgroundWidth: number; backgroundHeight: number } {
    // 基础宽高（考虑缩放）
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;
    const baseWidth =
      (style.width ? style.width : CONFIG.DEFAULT_BACKGROUND_WIDTH) * scaleX;
    const providedHeight =
      (style.height ? style.height : CONFIG.DEFAULT_BACKGROUND_HEIGHT) * scaleY;

    // 根据当前宽度对文本进行换行，计算需要的行数
    const wrappedText = this.wrapTextToWidth(caption.text, baseWidth, style);
    const lines = Math.max(1, wrappedText.split("\n").length);

    // 计算文本所需高度：行数 * 行高像素 + 垂直内边距
    const fontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;
    const lineHeight = style.lineHeight || 1;
    const textHeight = Math.ceil(
      lines * fontSize * lineHeight + CONFIG.PADDING_Y * 2
    );

    // 取较大值，避免文本溢出背景
    const backgroundHeight = Math.max(providedHeight, textHeight);

    return {
      backgroundWidth: baseWidth,
      backgroundHeight,
    };
  }

  /**
   * 计算文字位置
   */
  private static calculateTextPosition(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    textAlign: "left" | "center" | "right",
    style?: CaptionStyle
  ): { textX: number; textY: number } {
    // 垂直居中
    const textY = backgroundY + backgroundHeight / 2;

    // 计算缩放后的文字边距
    const scaleX = style?.scaleX || 1;
    const scaledTextMarginX = CONFIG.TEXT_MARGIN_X;

    // 水平位置根据对齐方式确定
    let textX: number;
    switch (textAlign) {
      case "left":
        textX = backgroundX + scaledTextMarginX;
        break;
      case "right":
        textX = backgroundX + backgroundWidth - scaledTextMarginX;
        break;
      default: // center
        textX = backgroundX + backgroundWidth / 2;
        break;
    }

    return { textX, textY };
  }

  /**
   * 生成文字位置标签
   * 根据布局信息在背景框内正确定位文字
   */
  private static generateTextPositionTags(
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): string {
    const xPos = Math.round(layoutInfo.textX);
    const yPos = Math.round(layoutInfo.textY);

    // 根据textAlign确定对齐方式
    let alignmentValue = CONFIG.ALIGNMENT_MAP[layoutInfo.textAlign] || 4; // 默认为左中对齐

    return `{\\an${alignmentValue}\\pos(${xPos},${yPos})}`;
  }

  /**
   * 生成颜色标签（支持渐变和普通颜色）
   */
  private static generateColorTags(style: CaptionStyle): string {
    return BaseASSUtils.generateColorTags(style);
  }

  /**
   * 生成字体样式标签（粗体、斜体等）
   * 注意：下划线和删除线已在ASS样式行中处理，不需要在Dialogue行中重复
   */
  private static generateFontStyleTags(style: CaptionStyle): string {
    const result = BaseASSUtils.generateFontStyleTags(style);
    if (result && style.styles && style.styles.length > 0) {
      console.log(`字体样式标签生成 - 下划线和删除线将通过ASS样式行处理`);
    }
    return result;
  }

  /**
   * 生成字符间距标签
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    // 对于文字元素，直接使用字符间距值，不进行缩放
    return BaseASSUtils.generateCharSpacingTags(style, false);
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;
    return BaseASSUtils.generateShadowTags(style, scaleX, scaleY);
  }

  /**
   * 生成描边标签
   */
  private static generateStrokeTags(style: CaptionStyle): string {
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;
    return BaseASSUtils.generateStrokeTags(style, scaleX, scaleY);
  }

  /**
   * 生成透明度标签
   */
  private static generateOpacityTags(
    style: CaptionStyle,
    elementOpacity?: number
  ): string {
    return BaseASSUtils.generateOpacityTags(style, elementOpacity);
  }

  /**
   * 生成行高标签
   */
  private static generateLineHeightTags(style: CaptionStyle): string {
    if (!style.lineHeight || style.lineHeight === 1) {
      return ""; // 默认行高，不需要标签
    }

    // 对于多行文本，我们需要在换行符处添加适当的间距
    // ASS中没有直接的行高控制，我们通过调整字符间距来模拟
    const lineSpacing = Math.round(
      (style.lineHeight - 1) *
        (style.fontSize || CONFIG.DEFAULT_FONT_SIZE) *
        CONFIG.LINE_SPACING_MULTIPLIER
    );

    if (lineSpacing > 0) {
      return `{\\fsp${lineSpacing}}`;
    }

    return "";
  }

  /**
   * 生成圆角矩形路径
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateRoundedRectanglePath(
    width: number,
    height: number,
    radius: number = CONFIG.DEFAULT_BORDER_RADIUS,
    scaleX: number = 1,
    scaleY: number = 1
  ): string {
    // 根据缩放因子调整圆角半径
    // 使用较小的缩放值来保持圆角比例的协调性
    const scale = Math.min(scaleX, scaleY);
    const adjustedRadius = radius * scale;

    // 确保圆角半径不超过矩形的一半
    const r = Math.min(adjustedRadius, Math.min(width, height) / 2);

    console.log(
      `圆角半径缩放: 原始(${radius}) -> 缩放后(${adjustedRadius}) -> 最终(${r}) [缩放:${scaleX}x${scaleY}, 使用:${scale}x]`
    );

    if (r <= 0) {
      // 如果圆角半径为0或负数，绘制普通矩形
      return `m 0 0 l ${width} 0 l ${width} ${height} l 0 ${height}`;
    }

    // 绘制圆角矩形路径
    // 从左上角圆角开始，顺时针绘制
    // 使用贝塞尔曲线近似圆角，控制点距离为半径的约0.552倍（圆的贝塞尔近似常数）
    const controlOffset = r * 0.552;

    const path = [
      `m ${r} 0`, // 移动到左上角圆角结束位置
      `l ${width - r} 0`, // 上边线
      `b ${width - r + controlOffset} 0 ${width} ${
        r - controlOffset
      } ${width} ${r}`, // 右上角圆角
      `l ${width} ${height - r}`, // 右边线
      `b ${width} ${height - r + controlOffset} ${
        width - r + controlOffset
      } ${height} ${width - r} ${height}`, // 右下角圆角
      `l ${r} ${height}`, // 下边线
      `b ${r - controlOffset} ${height} 0 ${height - r + controlOffset} 0 ${
        height - r
      }`, // 左下角圆角
      `l 0 ${r}`, // 左边线
      `b 0 ${r - controlOffset} ${r - controlOffset} 0 ${r} 0`, // 左上角圆角
    ].join(" ");

    return path;
  }

  /**
   * 生成背景矩形
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   * 使用相对坐标以支持move动画
   */
  private static generateBackgroundRectangle(
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): string {
    const bgColor = BaseASSUtils.convertColorToASS(style.backgroundColor);

    // 使用相对坐标，以背景框左上角为原点
    const width = layoutInfo.backgroundWidth;
    const height = layoutInfo.backgroundHeight;

    // 获取缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;

    // 生成圆角矩形路径，考虑缩放因子
    const roundedRectPath = this.generateRoundedRectanglePath(
      width,
      height,
      CONFIG.DEFAULT_BORDER_RADIUS,
      scaleX,
      scaleY
    );

    // 处理 RGBA 格式的透明度
    let alphaTag = "";
    if (style.backgroundColor) {
      const alpha = BaseASSUtils.extractAlphaFromColor(style.backgroundColor);
      if (alpha < 1) {
        // 转换透明度：0-1 -> 0-255 (ASS格式)
        const assAlpha = Math.round((1 - alpha) * 255);
        const alphaHex = assAlpha.toString(16).padStart(2, "0").toUpperCase();
        alphaTag = `\\1a&H${alphaHex}&\\3a&H${alphaHex}&`;
      }
    }

    // 使用an7对齐方式（左上角对齐）和pos定位到背景框位置
    // 这样move标签就能正常工作
    return `{\\an7\\pos(${layoutInfo.backgroundX},${layoutInfo.backgroundY})\\1c${bgColor}\\3c${bgColor}${alphaTag}\\p1}${roundedRectPath}{\\p0}`;
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth: number,
    canvasHeight: number
  ): string {
    return BaseASSUtils.generateASSHeader(
      canvasWidth,
      canvasHeight,
      "Text Element Subtitles"
    );
  }

  /**
   * 计算调整后的字体大小，确保文字适合背景框
   */
  private static calculateAdjustedFontSize(
    caption: Caption,
    layoutInfo: TextLayoutInfo,
    style: CaptionStyle
  ): number {
    const baseFontSize = style.fontSize || CONFIG.DEFAULT_FONT_SIZE;
    const scaleY = style.scaleY || 1;
    const originalFontSize = baseFontSize * scaleY; // 考虑缩放系数

    // 如果没有背景框，直接返回缩放后的字体大小
    if (!this.shouldRenderBackground(style)) {
      console.log(
        `字体大小 (考虑缩放${scaleY}): ${baseFontSize} -> ${originalFontSize}`
      );
      return originalFontSize;
    }

    return Math.round(originalFontSize);
  }

  /**
   * 为内容应用动画效果
   * 生成ASS动画标签，完全对应旧的drawtext方式的xfade滤镜效果
   */
  private static applyAnimationToContent(
    content: string,
    animationInfo: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string }
  ): string {
    let animatedContent = content;
    const animationTags: string[] = [];

    // 特殊处理fade动画：如果同时有入场和出场fade动画，需要合并为一个fad标签
    const hasFadeIn = animationInfo.fadeIn?.type === "fade";
    const hasFadeOut = animationInfo.fadeOut?.type === "fade";

    if (hasFadeIn && hasFadeOut) {
      // 同时有入场和出场fade动画，合并为一个fad标签
      const fadeInDuration = animationInfo.fadeIn.duration || 1000;
      const fadeOutDuration = animationInfo.fadeOut.duration || 1000;
      const combinedFadeTag = `\\fad(${fadeInDuration},${fadeOutDuration})`;
      animationTags.push(combinedFadeTag);
    } else {
      // 处理入场动画
      if (animationInfo.fadeIn) {
        const animationTag = this.generateAnimationTag(
          animationInfo.fadeIn,
          layoutInfo,
          timeInfo,
          "in"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }

      // 处理出场动画
      if (animationInfo.fadeOut) {
        const animationTag = this.generateAnimationTag(
          animationInfo.fadeOut,
          layoutInfo,
          timeInfo,
          "out"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }
    }

    // 将动画标签添加到内容前面
    if (animationTags.length > 0) {
      // 检查是否包含移动动画，如果有则移除pos标签避免冲突
      const hasMove = animationTags.some((tag) => tag.includes("\\move("));
      if (hasMove) {
        // 提取对齐标签，保留对齐方式
        const alignMatch = content.match(/\\an(\d+)/);
        const alignTag = alignMatch ? `\\an${alignMatch[1]}` : "";

        // 移除原有的pos标签，但保留对齐标签
        const contentWithoutPos = content.replace(
          /\{\\an\d+\\pos\([^)]+\)\}/,
          ""
        );

        // 将对齐标签添加到动画标签中
        const finalAnimationTags = alignTag
          ? `${alignTag}${animationTags.join("")}`
          : animationTags.join("");
        animatedContent = `{${finalAnimationTags}}${contentWithoutPos}`;
      } else {
        animatedContent = `{${animationTags.join("")}}${content}`;
      }
    }

    return animatedContent;
  }

  /**
   * 为背景框应用动画效果
   * 确保背景框在动画过程中始终跟随文字移动
   */
  private static applyAnimationToBackground(
    backgroundRect: string,
    animationInfo: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string }
  ): string {
    let animatedBackground = backgroundRect;
    const animationTags: string[] = [];

    // 特殊处理fade动画：如果同时有入场和出场fade动画，需要合并为一个fad标签
    const hasFadeIn = animationInfo.fadeIn?.type === "fade";
    const hasFadeOut = animationInfo.fadeOut?.type === "fade";

    if (hasFadeIn && hasFadeOut) {
      // 同时有入场和出场fade动画，合并为一个fad标签
      const fadeInDuration = animationInfo.fadeIn.duration || 1000;
      const fadeOutDuration = animationInfo.fadeOut.duration || 1000;
      const combinedFadeTag = `\\fad(${fadeInDuration},${fadeOutDuration})`;
      animationTags.push(combinedFadeTag);
    } else {
      // 处理入场动画
      if (animationInfo.fadeIn) {
        const animationTag = this.generateBackgroundAnimationTag(
          animationInfo.fadeIn,
          layoutInfo,
          timeInfo,
          "in"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }

      // 处理出场动画
      if (animationInfo.fadeOut) {
        const animationTag = this.generateBackgroundAnimationTag(
          animationInfo.fadeOut,
          layoutInfo,
          timeInfo,
          "out"
        );
        if (animationTag) {
          animationTags.push(animationTag);
        }
      }
    }

    // 将动画标签添加到背景内容前面
    if (animationTags.length > 0) {
      // 提取背景矩形的位置信息并替换为动画位置
      animatedBackground = this.replaceBackgroundPosition(
        backgroundRect,
        animationTags.join("")
      );
    }

    return animatedBackground;
  }

  /**
   * 替换背景矩形的位置信息
   * 将pos标签替换为move标签以支持动画
   */
  private static replaceBackgroundPosition(
    backgroundRect: string,
    animationTags: string
  ): string {
    // 检查是否包含move动画标签
    if (animationTags.includes("\\move(")) {
      // 移除原有的pos标签，因为move标签会覆盖pos标签
      const withoutPos = backgroundRect.replace(/\\pos\([^)]+\)/, "");

      // 在样式标签中插入动画标签
      const match = withoutPos.match(/^(\{[^}]*\})(.*)/);
      if (match) {
        const [, styleTags, content] = match;
        // 在样式标签末尾插入动画标签
        const newStyleTags = styleTags.slice(0, -1) + animationTags + "}";
        return newStyleTags + content;
      }
    } else {
      // 对于非移动动画（如fade），保持原有的pos标签
      const match = backgroundRect.match(/^(\{[^}]*\})(.*)/);
      if (match) {
        const [, styleTags, content] = match;
        // 在样式标签末尾插入动画标签
        const newStyleTags = styleTags.slice(0, -1) + animationTags + "}";
        return newStyleTags + content;
      }
    }

    // 如果没有匹配到，直接在前面添加动画标签
    return `{${animationTags}}${backgroundRect}`;
  }

  /**
   * 生成背景框专用的动画标签
   * 计算背景框的移动轨迹，确保始终框住文字
   */
  private static generateBackgroundAnimationTag(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const animationType = animation.type;

    switch (animationType) {
      case "fade":
        return this.generateFadeAnimation(animation, timeInfo, direction);

      case "slide":
        return this.generateBackgroundMoveAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "wipe":
        // 背景框的wipe动画也使用clip效果，与文字保持一致
        const duration = animation.duration || 1000;
        const wipeDirection = animation.wipeDirection || "left";

        // 计算动画时间 - 使用相对于字幕开始时间的偏移量
        const subtitleStartTime = this.parseTimeToMs(timeInfo.startTime);
        const subtitleEndTime = this.parseTimeToMs(timeInfo.endTime);

        let animStartTime: number;
        let animEndTime: number;

        if (direction === "in") {
          // 入场动画：从字幕开始时立即开始动画
          animStartTime = 0; // 相对于字幕开始的偏移量
          animEndTime = duration; // 动画持续时间
        } else {
          // 出场动画：在字幕结束前开始动画
          const totalDuration = subtitleEndTime - subtitleStartTime;
          animStartTime = totalDuration - duration; // 在字幕结束前duration毫秒开始
          animEndTime = totalDuration; // 在字幕结束时结束
        }

        return this.generateWipeClipAnimation(
          layoutInfo,
          wipeDirection,
          direction,
          animStartTime,
          animEndTime
        );

      case "zoom":
        return this.generateZoomAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      default:
        return this.generateFadeAnimation(
          { ...animation, type: "fade" },
          timeInfo,
          direction
        );
    }
  }

  /**
   * 生成背景框的移动动画
   * 计算背景框的起始和结束位置，确保跟随文字移动
   */
  private static generateBackgroundMoveAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;
    const moveDirection =
      animation.slideDirection || animation.wipeDirection || "left";

    // 计算背景框的移动位置
    const { startX, startY, endX, endY } =
      this.calculateBackgroundMovePositions(
        layoutInfo,
        moveDirection,
        direction
      );

    // ASS中的时间应该是相对于字幕开始时间的偏移量，而不是绝对时间
    const subtitleStartTime = this.parseTimeToMs(timeInfo.startTime);
    const subtitleEndTime = this.parseTimeToMs(timeInfo.endTime);

    let moveStartTime: number;
    let moveEndTime: number;

    if (direction === "in") {
      // 入场动画：从字幕开始时立即开始动画
      moveStartTime = 0; // 相对于字幕开始的偏移量
      moveEndTime = duration; // 动画持续时间
    } else {
      // 出场动画：在字幕结束前开始动画
      const totalDuration = subtitleEndTime - subtitleStartTime;
      moveStartTime = totalDuration - duration; // 在字幕结束前duration毫秒开始
      moveEndTime = totalDuration; // 在字幕结束时结束
    }

    // 使用ASS的move标签实现背景框移动
    return `\\move(${startX},${startY},${endX},${endY},${moveStartTime},${moveEndTime})`;
  }

  /**
   * 计算背景框的移动位置
   * 确保背景框始终跟随文字移动，从视频外部开始
   */
  private static calculateBackgroundMovePositions(
    layoutInfo: TextLayoutInfo,
    moveDirection: string,
    direction: "in" | "out"
  ): { startX: number; startY: number; endX: number; endY: number } {
    // 背景框的正常位置
    const normalX = layoutInfo.backgroundX;
    const normalY = layoutInfo.backgroundY;

    // 获取画布尺寸
    const canvasWidth = 1920;
    const canvasHeight = 1080;

    let startX = normalX;
    let startY = normalY;
    let endX = normalX;
    let endY = normalY;

    if (direction === "in") {
      // 入场动画：背景框从视频外部移动到正常位置
      endX = normalX;
      endY = normalY;

      switch (moveDirection) {
        case "left":
          // 从左侧视频外部开始
          startX = -200 - layoutInfo.backgroundWidth; // 确保背景框完全在外部
          break;
        case "right":
          // 从右侧视频外部开始
          startX = canvasWidth + 200;
          break;
        case "up":
          // 从上方视频外部开始
          startY = -100 - layoutInfo.backgroundHeight;
          break;
        case "down":
          // 从下方视频外部开始
          startY = canvasHeight + 100;
          break;
      }
    } else {
      // 出场动画：背景框从正常位置移动到视频外部
      startX = normalX;
      startY = normalY;

      switch (moveDirection) {
        case "left":
          // 移动到左侧视频外部
          endX = -200 - layoutInfo.backgroundWidth;
          break;
        case "right":
          // 移动到右侧视频外部
          endX = canvasWidth + 200;
          break;
        case "up":
          // 移动到上方视频外部
          endY = -100 - layoutInfo.backgroundHeight;
          break;
        case "down":
          // 移动到下方视频外部
          endY = canvasHeight + 100;
          break;
      }
    }

    return { startX, startY, endX, endY };
  }

  /**
   * 生成统一的动画标签
   * 支持fade、slide、wipe、zoom等所有动画类型
   */
  private static generateAnimationTag(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const animationType = animation.type;
    const duration = animation.duration || 1000;

    switch (animationType) {
      case "fade":
        return this.generateFadeAnimation(animation, timeInfo, direction);

      case "slide":
        return this.generateSlideAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "wipe":
        return this.generateWipeAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      case "zoom":
        return this.generateZoomAnimation(
          animation,
          layoutInfo,
          timeInfo,
          direction
        );

      default:
        console.log(`不支持的动画类型: ${animationType}，使用fade作为默认`);
        return this.generateFadeAnimation(
          { ...animation, type: "fade" },
          timeInfo,
          direction
        );
    }
  }

  /**
   * 生成淡入淡出动画标签
   * 统一处理fade动画的入场和出场效果
   */
  private static generateFadeAnimation(
    animation: any,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    return BaseASSUtils.generateFadeAnimation(animation, timeInfo, direction);
  }

  /**
   * 生成滑动动画标签
   */
  private static generateSlideAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    if (animation.type !== "slide") {
      return "";
    }

    const duration = animation.duration || 1000;
    const slideDirection = animation.slideDirection || "left";

    // 计算滑动的起始和结束位置
    const { startX, startY, endX, endY } = this.calculateSlidePositions(
      layoutInfo,
      slideDirection,
      direction
    );

    // 使用ASS的move标签实现滑动效果
    // ASS中的时间应该是相对于字幕开始时间的偏移量，而不是绝对时间
    const subtitleStartTime = this.parseTimeToMs(timeInfo.startTime);
    const subtitleEndTime = this.parseTimeToMs(timeInfo.endTime);

    let moveStartTime: number;
    let moveEndTime: number;

    if (direction === "in") {
      // 入场动画：从字幕开始时立即开始动画
      moveStartTime = 0; // 相对于字幕开始的偏移量
      moveEndTime = duration; // 动画持续时间
    } else {
      // 出场动画：在字幕结束前开始动画
      const totalDuration = subtitleEndTime - subtitleStartTime;
      moveStartTime = totalDuration - duration; // 在字幕结束前duration毫秒开始
      moveEndTime = totalDuration; // 在字幕结束时结束
    }

    return `\\move(${startX},${startY},${endX},${endY},${moveStartTime},${moveEndTime})`;
  }

  /**
   * 计算滑动位置
   * 确保slide动画从视频外部开始切入
   */
  private static calculateSlidePositions(
    layoutInfo: TextLayoutInfo,
    slideDirection: string,
    direction: "in" | "out"
  ): { startX: number; startY: number; endX: number; endY: number } {
    const normalX = layoutInfo.textX;
    const normalY = layoutInfo.textY;

    // 获取画布尺寸（假设从全局或传递进来，这里使用常见的1920x1080）
    const canvasWidth = 1920;
    const canvasHeight = 1080;

    let startX = normalX;
    let startY = normalY;
    let endX = normalX;
    let endY = normalY;

    if (direction === "in") {
      // 入场动画：从视频外部滑动到正常位置
      endX = normalX;
      endY = normalY;

      switch (slideDirection) {
        case "left":
          // 从左侧视频外部开始（负坐标）
          startX = -200; // 确保完全在视频外部
          break;
        case "right":
          // 从右侧视频外部开始
          startX = canvasWidth + 200;
          break;
        case "up":
          // 从上方视频外部开始
          startY = -100;
          break;
        case "down":
          // 从下方视频外部开始
          startY = canvasHeight + 100;
          break;
      }
    } else {
      // 出场动画：从正常位置滑动到视频外部
      startX = normalX;
      startY = normalY;

      switch (slideDirection) {
        case "left":
          // 滑动到左侧视频外部
          endX = -200;
          break;
        case "right":
          // 滑动到右侧视频外部
          endX = canvasWidth + 200;
          break;
        case "up":
          // 滑动到上方视频外部
          endY = -100;
          break;
        case "down":
          // 滑动到下方视频外部
          endY = canvasHeight + 100;
          break;
      }
    }

    return { startX, startY, endX, endY };
  }

  /**
   * 生成擦除动画标签
   * 使用clip标签实现真正的擦除效果，对应xfade的wipe系列
   */
  private static generateWipeAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;
    const wipeDirection = animation.wipeDirection || "left";

    // 计算动画时间 - 使用相对于字幕开始时间的偏移量
    const subtitleStartTime = this.parseTimeToMs(timeInfo.startTime);
    const subtitleEndTime = this.parseTimeToMs(timeInfo.endTime);

    let animStartTime: number;
    let animEndTime: number;

    if (direction === "in") {
      // 入场动画：从字幕开始时立即开始动画
      animStartTime = 0; // 相对于字幕开始的偏移量
      animEndTime = duration; // 动画持续时间
    } else {
      // 出场动画：在字幕结束前开始动画
      const totalDuration = subtitleEndTime - subtitleStartTime;
      animStartTime = totalDuration - duration; // 在字幕结束前duration毫秒开始
      animEndTime = totalDuration; // 在字幕结束时结束
    }

    // 生成动态clip标签实现擦除效果
    const clipAnimation = this.generateWipeClipAnimation(
      layoutInfo,
      wipeDirection,
      direction,
      animStartTime,
      animEndTime
    );

    return clipAnimation;
  }

  /**
   * 生成擦除动画的clip标签
   * 使用ASS的\clip()标签实现真正的擦除效果
   */
  private static generateWipeClipAnimation(
    layoutInfo: TextLayoutInfo,
    wipeDirection: string,
    direction: "in" | "out",
    startTime: number,
    endTime: number
  ): string {
    // 计算文字区域边界（使用背景框位置和尺寸）
    const bgX = Math.round(layoutInfo.backgroundX);
    const bgY = Math.round(layoutInfo.backgroundY);
    const bgWidth = Math.round(layoutInfo.backgroundWidth + 10);
    const bgHeight = Math.round(layoutInfo.backgroundHeight + 10);

    // 计算完整的显示区域
    const left = bgX;
    const right = bgX + bgWidth;
    const top = bgY;
    const bottom = bgY + bgHeight;

    // 根据擦除方向和动画方向生成clip标签
    let clipTag: string;

    switch (wipeDirection) {
      case "left":
        if (direction === "in") {
          // 左擦除入场：从左边开始逐渐显示
          // 起始：只显示最左边的一条线
          // 结束：显示完整区域
          clipTag = `\\clip(${left},${top},${
            left + 1
          },${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 左擦除出场：从右边开始逐渐隐藏
          // 起始：显示完整区域
          // 结束：只显示最左边的一条线
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${
            left + 1
          },${bottom}))`;
        }
        break;

      case "right":
        if (direction === "in") {
          // 右擦除入场：从右边开始逐渐显示
          clipTag = `\\clip(${
            right - 1
          },${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 右擦除出场：从左边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${
            right - 1
          },${top},${right},${bottom}))`;
        }
        break;

      case "up":
        if (direction === "in") {
          // 上擦除入场：从上边开始逐渐显示
          clipTag = `\\clip(${left},${top},${right},${
            top + 1
          })\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 上擦除出场：从下边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${
            top + 1
          }))`;
        }
        break;

      case "down":
        if (direction === "in") {
          // 下擦除入场：从下边开始逐渐显示
          clipTag = `\\clip(${left},${
            bottom - 1
          },${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
        } else {
          // 下擦除出场：从上边开始逐渐隐藏
          clipTag = `\\clip(${left},${top},${right},${bottom})\\t(${startTime},${endTime},\\clip(${left},${
            bottom - 1
          },${right},${bottom}))`;
        }
        break;

      default:
        // 默认左擦除
        clipTag = `\\clip(${left},${top},${
          left + 1
        },${bottom})\\t(${startTime},${endTime},\\clip(${left},${top},${right},${bottom}))`;
    }

    return clipTag;
  }

  /**
   * 生成缩放动画标签
   * 使用fscx/fscy标签实现缩放效果，对应xfade的circleopen
   */
  private static generateZoomAnimation(
    animation: any,
    layoutInfo: TextLayoutInfo,
    timeInfo: { startTime: string; endTime: string },
    direction: "in" | "out"
  ): string {
    const duration = animation.duration || 1000;

    // 计算缩放参数
    const startScale = direction === "in" ? 0 : 100;
    const endScale = direction === "in" ? 100 : 0;

    // 计算动画时间 - 使用相对于字幕开始时间的偏移量
    const subtitleStartTime = this.parseTimeToMs(timeInfo.startTime);
    const subtitleEndTime = this.parseTimeToMs(timeInfo.endTime);

    let animStartTime: number;
    let animEndTime: number;

    if (direction === "in") {
      // 入场动画：从字幕开始时立即开始动画
      animStartTime = 0; // 相对于字幕开始的偏移量
      animEndTime = duration; // 动画持续时间
    } else {
      // 出场动画：在字幕结束前开始动画
      const totalDuration = subtitleEndTime - subtitleStartTime;
      animStartTime = totalDuration - duration; // 在字幕结束前duration毫秒开始
      animEndTime = totalDuration; // 在字幕结束时结束
    }

    // 使用ASS的transform标签实现缩放动画
    return `\\t(${animStartTime},${animEndTime},\\fscx${endScale}\\fscy${endScale})`;
  }

  /**
   * 解析时间字符串为毫秒
   */
  private static parseTimeToMs(timeStr: string): number {
    return BaseASSUtils.parseTimeToMs(timeStr);
  }
}
