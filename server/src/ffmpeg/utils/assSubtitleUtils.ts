import { Caption, CaptionStyle } from "../types";
import { ASS_CONFIG } from "../config";
import {
  BaseASSUtils,
  COMMON_ASS_CONSTANTS,
  ASS_ALIGNMENT_MAP,
} from "./baseASSUtils";

/**
 * ASS字幕工具类
 * 提供高级的ASS字幕生成和处理功能
 */
export class ASSSubtitleUtils {
  /**
   * 转换颜色格式：从 #RRGGBB 到 &HBBGGRR& (ASS格式)
   */
  static convertColorToASS(hexColor: string): string {
    return BaseASSUtils.convertColorToASS(hexColor);
  }

  /**
   * 转换对齐方式到ASS格式
   */
  static getASSAlignment(textAlign: string): number {
    return BaseASSUtils.getASSAlignment(textAlign);
  }

  /**
   * 转换时间格式到ASS格式
   */
  static convertTimeToASS(timeStr: string): string {
    return BaseASSUtils.convertTimeToASS(timeStr);
  }

  /**
   * 转义ASS文本
   */
  static escapeASSText(text: string): string {
    return BaseASSUtils.escapeASSText(text);
  }

  /**
   * 计算边距
   */
  private static calculateMargins(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): { sideMargin: number; bottomMargin: number } {
    // 计算底部边距
    let bottomMargin = canvasHeight
      ? Math.round(canvasHeight * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (style.positionY !== undefined && canvasHeight) {
      bottomMargin = Math.max(0, bottomMargin - style.positionY);
    }

    // 计算侧边距
    let sideMargin = canvasWidth
      ? Math.round(canvasWidth * ASS_CONFIG.DEFAULT_MARGIN_RATIO)
      : 10;

    if (canvasWidth) {
      const effectiveFrontendTextAreaWidth = Math.max(
        0,
        canvasWidth * COMMON_ASS_CONSTANTS.FRONTEND_TEXTBOX_WIDTH_RATIO -
          2 * COMMON_ASS_CONSTANTS.FRONTEND_TEXTBOX_PADDING
      );

      sideMargin = Math.round(
        (canvasWidth - effectiveFrontendTextAreaWidth) / 2
      );
    }

    return { sideMargin, bottomMargin };
  }

  /**
   * 生成ASS样式字符串 - 支持scale缩放
   */
  static generateASSStyle(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    const isBold = style.styles.includes("bold") || style.fontWeight >= 700;
    const isItalic = style.styles.includes("italic");
    const hasUnderline =
      style.styles.includes("underline") || style.styles.includes("underlined");
    const isStrikethrough =
      style.styles.includes("strikethrough") ||
      style.styles.includes("strikeOut");

    // 使用 -1 来实现连续下划线效果，这比普通下划线(1)效果更好
    const underlineValue = hasUnderline ? -1 : 0;

    const { sideMargin, bottomMargin } = this.calculateMargins(
      style,
      canvasWidth,
      canvasHeight
    );

    // 应用scale到字体大小 - 与text元素保持一致
    const baseFontSize = style.fontSize || ASS_CONFIG.DEFAULT_STYLE.fontSize;
    const scaleY = style.scaleY || 1;
    const scaledFontSize = Math.round(baseFontSize * scaleY);

    // 应用scale到描边宽度 - 与text元素保持一致
    const baseStrokeWidth = style.strokeWidth || 0;
    const scaleX = style.scaleX || 1;
    const scaledStrokeWidth = Math.max(0, Math.round(baseStrokeWidth * scaleX));

    console.log(
      `字幕scale应用: 字体大小 ${baseFontSize} -> ${scaledFontSize} (scaleY: ${scaleY}), 描边宽度 ${baseStrokeWidth} -> ${scaledStrokeWidth} (scaleX: ${scaleX})`
    );

    const styleComponents = [
      style.fontFamily || ASS_CONFIG.DEFAULT_STYLE.fontFamily,
      scaledFontSize, // 使用缩放后的字体大小
      BaseASSUtils.convertColorToASS(style.fontColor),
      BaseASSUtils.convertColorToASS(style.fontColor),
      BaseASSUtils.convertColorToASS(style.strokeColor),
      BaseASSUtils.convertColorToASS(style.backgroundColor),
      isBold ? 1 : 0,
      isItalic ? 1 : 0,
      underlineValue, // 使用 -1 实现连续下划线效果
      isStrikethrough ? 1 : 0, // 修复：正确处理删除线
      COMMON_ASS_CONSTANTS.SCALE_VALUES.X,
      COMMON_ASS_CONSTANTS.SCALE_VALUES.Y,
      Math.round(
        (style.charSpacing || 0) * COMMON_ASS_CONSTANTS.CHAR_SPACING_RATIO
      ),
      0, // Angle
      COMMON_ASS_CONSTANTS.BORDER_STYLE,
      scaledStrokeWidth, // 使用缩放后的描边宽度
      0, // Shadow - 设置为0避免文字模糊，阴影效果通过标签实现
      5, // 使用中心对齐作为默认值，实际对齐通过位置标签控制
      sideMargin, // MarginL
      sideMargin, // MarginR
      bottomMargin, // MarginV
      COMMON_ASS_CONSTANTS.ENCODING,
    ];

    console.log(
      `ASS样式生成 - 下划线参数: ${underlineValue} (${
        hasUnderline ? "启用连续下划线" : "无下划线"
      })`
    );

    return styleComponents.join(",");
  }

  /**
   * 获取ASS对齐值 - 改为左上角对齐系统
   */
  private static getASSAlignmentValue(
    originX?: string,
    originY?: string
  ): number {
    // 改为默认使用左上角对齐
    const vertical = originY || "top";
    const horizontal = originX || "left";
    const key = `${vertical}-${horizontal}` as keyof typeof ASS_ALIGNMENT_MAP;

    return ASS_ALIGNMENT_MAP[key] || ASS_ALIGNMENT_MAP["top-left"];
  }

  /**
   * 计算背景框尺寸 - 支持scale缩放和多行文本
   */
  private static calculateBackgroundDimensions(
    style: CaptionStyle,
    text?: string
  ): {
    backgroundWidth: number;
    backgroundHeight: number;
  } {
    // 获取缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;

    // 计算基础宽度
    const baseWidth = style.width || 400; // 默认宽度

    // 计算基础高度 - 与前端逻辑保持一致
    const baseFontSize = style.fontSize || 35;
    const lineHeight = style.lineHeight || 1.2;

    // 计算行数 - 如果有文本则使用实际行数，否则使用默认值1
    const lineCount = text ? text.split("\n").length : 1;

    // 与前端calculateCaptionBackgroundHeight保持一致的计算逻辑
    const textHeight = baseFontSize * lineHeight * lineCount;
    const padding = 20; // 上下内边距，与前端保持一致
    const baseHeight = textHeight + padding;

    // 应用缩放因子
    const backgroundWidth = baseWidth * scaleX;
    const backgroundHeight = baseHeight * scaleY;

    console.log(
      `字幕背景框尺寸计算: 文本行数(${lineCount}) 基础(${baseWidth}x${baseHeight}) -> 缩放后(${backgroundWidth}x${backgroundHeight}) [scale: ${scaleX}x${scaleY}]`
    );

    return { backgroundWidth, backgroundHeight };
  }

  /**
   * 计算位置坐标 - 改为左上角坐标系统
   * 注意：这里计算的是背景框左上角的坐标，与前端保持一致
   */
  private static calculatePosition(
    style: CaptionStyle,
    canvasWidth: number,
    canvasHeight: number,
    text?: string
  ): { xPos: number; yPos: number } {
    // 使用新的背景框尺寸计算方法（包含scale和多行文本）
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, text);

    // 计算水平位置 - 以背景框左上角为基准
    let xPos = 0;
    switch (style.originX) {
      case "left":
        xPos = 0 + (style.positionX || 0);
        break;
      case "right":
        xPos = canvasWidth - backgroundWidth + (style.positionX || 0);
        break;
      case "center":
      default:
        xPos = (canvasWidth - backgroundWidth) / 2 + (style.positionX || 0);
        break;
    }

    // 计算垂直位置 - 以背景框左上角为基准
    let yPos = 0;
    const defaultBottomMarginRatio = ASS_CONFIG.DEFAULT_MARGIN_RATIO;

    switch (style.originY) {
      case "top":
        yPos = 0 + (style.positionY || 0);
        break;
      case "center":
        yPos = (canvasHeight - backgroundHeight) / 2 + (style.positionY || 0);
        break;
      case "bottom":
      default:
        const bottomMargin = Math.round(
          canvasHeight * defaultBottomMarginRatio
        );
        yPos =
          canvasHeight -
          bottomMargin -
          backgroundHeight +
          (style.positionY || 0);
        break;
    }

    return { xPos: Math.round(xPos), yPos: Math.round(yPos) };
  }

  /**
   * 生成位置标签 - 支持背景框内文本对齐和scale缩放
   */
  private static generatePositionTags(
    style: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number,
    text?: string
  ): string {
    if (!canvasWidth || !canvasHeight) {
      return "";
    }

    // 使用新的背景框尺寸计算方法（包含scale和多行文本）
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, text);

    // 计算背景框位置（左上角坐标）
    const { xPos: backgroundX, yPos: backgroundY } = this.calculatePosition(
      style,
      canvasWidth,
      canvasHeight,
      text
    );

    // 计算文本在背景框内的位置
    const { textX, textY } = this.calculateTextPositionInBackground(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style.textAlign || "center"
    );

    // 根据textAlign确定ASS对齐方式
    let alignmentValue = 4; // 默认为左中对齐
    switch (style.textAlign) {
      case "left":
        alignmentValue = 4; // 左中对齐
        break;
      case "right":
        alignmentValue = 6; // 右中对齐
        break;
      case "center":
      default:
        alignmentValue = 5; // 中心对齐
        break;
    }

    console.log(
      `字幕位置标签生成: 背景框(${backgroundX},${backgroundY},${backgroundWidth}x${backgroundHeight}) -> 文本位置(${textX},${textY}) [对齐: \\an${alignmentValue}]`
    );

    return `{\\\\an${alignmentValue}\\\\pos(${Math.round(textX)},${Math.round(
      textY
    )})}`;
  }

  /**
   * 计算文本在背景框内的位置
   */
  private static calculateTextPositionInBackground(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    textAlign: "left" | "center" | "right"
  ): { textX: number; textY: number } {
    // 垂直居中
    const textY = backgroundY + backgroundHeight / 2;

    // 文本边距
    const textMarginX = 10;

    // 水平位置根据对齐方式确定
    let textX: number;
    switch (textAlign) {
      case "left":
        textX = backgroundX + textMarginX;
        break;
      case "right":
        textX = backgroundX + backgroundWidth - textMarginX;
        break;
      default: // center
        textX = backgroundX + backgroundWidth / 2;
        break;
    }

    return { textX, textY };
  }

  /**
   * 生成渐变标签
   */
  private static generateGradientTags(style: CaptionStyle): string {
    if (
      !style.useGradient ||
      !style.gradientColors ||
      style.gradientColors.length < 2
    ) {
      return "";
    }

    const color1 = this.convertColorToASS(style.gradientColors[0]);
    const color2 = this.convertColorToASS(style.gradientColors[1]);
    return `{\\1c${color1}\\3c${color2}}`;
  }

  /**
   * 生成字符间距标签 - 支持scale缩放
   */
  private static generateCharSpacingTags(style: CaptionStyle): string {
    if (!style.charSpacing || style.charSpacing === 0) {
      return "";
    }

    // 应用scale到字符间距
    const scaleX = style.scaleX || 1;
    const scaledCharSpacing = style.charSpacing * scaleX;
    const assCharSpacing = Math.round(
      scaledCharSpacing * COMMON_ASS_CONSTANTS.CHAR_SPACING_RATIO
    );

    console.log(
      `字符间距scale应用: 原始(${style.charSpacing}) -> 缩放后(${scaledCharSpacing}) -> ASS值(${assCharSpacing}) [scaleX: ${scaleX}]`
    );

    return `{\\fsp${assCharSpacing}}`;
  }

  /**
   * 生成阴影标签
   */
  private static generateShadowTags(style: CaptionStyle): string {
    const scaleX = (style as any).scaleX || 1;
    const scaleY = (style as any).scaleY || 1;
    return BaseASSUtils.generateShadowTags(style, scaleX, scaleY);
  }

  /**
   * 根据宽度限制自动换行文字
   * 复用文字元素的换行逻辑，确保字幕也能正确换行
   */
  private static wrapTextToWidth(
    text: string,
    maxWidth: number,
    style: CaptionStyle
  ): string {
    if (!text || maxWidth <= 0) {
      return text;
    }

    // 如果文本已经包含手动换行符，先按换行符分割
    const paragraphs = text.split("\n");
    const wrappedParagraphs: string[] = [];

    for (const paragraph of paragraphs) {
      if (paragraph.trim() === "") {
        wrappedParagraphs.push("");
        continue;
      }

      // 对每个段落进行自动换行
      const wrappedLines = this.wrapParagraphToWidth(
        paragraph,
        maxWidth,
        style
      );
      wrappedParagraphs.push(wrappedLines);
    }

    return wrappedParagraphs.join("\n");
  }

  /**
   * 对单个段落进行换行处理
   */
  private static wrapParagraphToWidth(
    paragraph: string,
    maxWidth: number,
    style: CaptionStyle
  ): string {
    const fontSize = style.fontSize || 35; // 字幕默认字体大小
    const charSpacing = style.charSpacing || 0;

    // 如果整段文本在宽度内，则不换行，直接返回
    if (this.fitsInWidth(paragraph, maxWidth, style)) {
      return paragraph;
    }

    // 计算可用宽度（减去内边距和安全边距）
    const padding = 5; // 字幕内边距

    const availableWidth = maxWidth;

    if (availableWidth <= 0) {
      return paragraph;
    }

    // 估算每个字符的平均宽度
    const avgCharWidth = this.estimateCharWidth(fontSize, style.fontFamily);

    // 考虑字符间距
    const effectiveCharWidth = avgCharWidth + charSpacing;

    // 估算每行可容纳的字符数，使用更保守的计算
    const maxCharsPerLine = Math.floor(availableWidth / effectiveCharWidth);

    if (maxCharsPerLine <= 0) {
      return paragraph;
    }

    // 按单词和字符进行智能换行
    return this.intelligentWrap(paragraph, maxCharsPerLine);
  }

  /**
   * 智能换行：优先在单词边界换行，中文字符可以任意位置换行
   * 使用更精确的宽度计算，考虑中英文字符宽度差异
   */
  private static intelligentWrap(
    text: string,
    maxCharsPerLine: number
  ): string {
    if (text.length <= maxCharsPerLine) {
      return text;
    }

    const lines: string[] = [];
    let currentLine = "";
    let currentWidth = 0;
    let i = 0;

    while (i < text.length) {
      const char = text[i];

      // 估算当前字符的宽度（中文字符通常比英文字符宽）
      const charWidth = this.isCJKCharacter(char) ? 1.5 : 1;

      // 检查添加当前字符后是否超出限制
      if (currentWidth + charWidth > maxCharsPerLine) {
        // 尝试在单词边界换行（仅对英文）
        const lastSpaceIndex = currentLine.lastIndexOf(" ");

        if (lastSpaceIndex > 0 && this.isEnglishText(currentLine)) {
          // 在最后一个空格处换行
          lines.push(currentLine.substring(0, lastSpaceIndex));
          currentLine = currentLine.substring(lastSpaceIndex + 1) + char;
          // 重新计算当前行宽度
          currentWidth = this.calculateLineWidth(currentLine);
        } else {
          // 直接换行
          lines.push(currentLine);
          currentLine = char;
          currentWidth = charWidth;
        }
      } else {
        currentLine += char;
        currentWidth += charWidth;
      }

      i++;
    }

    // 添加最后一行
    if (currentLine) {
      lines.push(currentLine);
    }

    return lines.join("\n");
  }

  /**
   * 检查字符是否为中日韩字符
   */
  private static isCJKCharacter(char: string): boolean {
    const code = char.charCodeAt(0);
    return (
      (code >= 0x4e00 && code <= 0x9fff) || // 中文
      (code >= 0x3400 && code <= 0x4dbf) || // 中文扩展A
      (code >= 0x3040 && code <= 0x309f) || // 日文平假名
      (code >= 0x30a0 && code <= 0x30ff) || // 日文片假名
      (code >= 0xac00 && code <= 0xd7af) // 韩文
    );
  }

  /**
   * 计算行的实际宽度（考虑中英文字符差异）
   */
  private static calculateLineWidth(line: string): number {
    let width = 0;
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      width += this.isCJKCharacter(char) ? 1.5 : 1;
    }
    return width;
  }

  /**
   * 估算字符宽度
   * 更精确的字符宽度估算，考虑不同字体和字符类型
   */
  private static estimateCharWidth(
    fontSize: number,
    fontFamily?: string
  ): number {
    // 根据字体类型调整宽度比例
    const isMonospace = fontFamily && fontFamily.toLowerCase().includes("mono");

    if (isMonospace) {
      return fontSize * 0.6; // 等宽字体
    }

    // 对于比例字体，使用更保守的估算
    // 考虑到中文字符通常比英文字符宽，使用更大的系数
    return fontSize * 0.8; // 增加系数以确保不超出边界
  }

  /**
   * 检查文本是否主要是英文
   */
  private static isEnglishText(text: string): boolean {
    const englishChars = text.match(/[a-zA-Z\s]/g);
    return englishChars ? englishChars.length / text.length > 0.7 : false;
  }

  /**
   * 估算整段文本的像素宽度（更精确，用于判断是否需要换行）
   */
  private static estimateTextWidthPx(
    text: string,
    fontSize: number,
    fontFamily?: string,
    charSpacing: number = 0
  ): number {
    let width = 0;
    for (let i = 0; i < text.length; i++) {
      const ch = text[i];
      width += this.estimateCharPixelWidth(ch, fontSize, fontFamily);
      if (i > 0) width += charSpacing;
    }
    return width;
  }

  /**
   * 估算单个字符的像素宽度（区分中英文、空格、标点）
   */
  private static estimateCharPixelWidth(
    ch: string,
    fontSize: number,
    fontFamily?: string
  ): number {
    const isMono = fontFamily && fontFamily.toLowerCase().includes("mono");
    if (ch === " ") return fontSize * (isMono ? 0.5 : 0.33);
    // CJK字符近似等宽
    if (this.isCJKCharacter(ch)) return fontSize * (isMono ? 0.95 : 1.0);
    // 常见英文/数字字符
    if (/^[A-Za-z0-9]$/.test(ch)) return fontSize * (isMono ? 0.6 : 0.55);
    // 其他标点与符号，取中间值
    return fontSize * (isMono ? 0.55 : 0.5);
  }

  /**
   * 判断一段文本在给定宽度内是否可单行放下
   */
  private static fitsInWidth(
    paragraph: string,
    maxWidth: number,
    style: CaptionStyle
  ): boolean {
    const fontSize = style.fontSize || 35; // 字幕默认字体大小
    const charSpacing = style.charSpacing || 0;
    const padding = 20; // 字幕内边距，与字幕渲染一致
    const availableWidth = Math.max(0, maxWidth - padding); // 不减安全边距用于更宽松判断
    const textWidth = this.estimateTextWidthPx(
      paragraph,
      fontSize,
      style.fontFamily,
      charSpacing
    );
    return textWidth <= availableWidth;
  }

  /**
   * 处理单个字幕文本
   */
  private static processSubtitleText(
    caption: Caption,
    finalStyle: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    // 首先进行自动换行处理，确保文字不超出背景框边界
    let processedText = caption.text;
    if (finalStyle.width && finalStyle.width > 0) {
      processedText = this.wrapTextToWidth(
        caption.text,
        finalStyle.width,
        finalStyle
      );
    }

    let escapedText = this.escapeASSText(processedText);

    // 按优先级添加各种标签
    const positionTags = this.generatePositionTags(
      finalStyle,
      canvasWidth,
      canvasHeight,
      processedText // 使用换行后的文本计算位置
    );
    const gradientTags = this.generateGradientTags(finalStyle);
    const charSpacingTags = this.generateCharSpacingTags(finalStyle);
    const shadowTags = this.generateShadowTags(finalStyle);

    // 组合所有标签
    return (
      positionTags + gradientTags + charSpacingTags + shadowTags + escapedText
    );
  }

  /**
   * 生成ASS文件头部
   */
  private static generateASSHeader(
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    return BaseASSUtils.generateASSHeader(
      canvasWidth || 1920,
      canvasHeight || 1080,
      "Generated Subtitles"
    );
  }

  /**
   * 判断是否应该渲染背景
   */
  private static shouldRenderBackground(style: CaptionStyle): boolean {
    return !!(style.backgroundColor && style.backgroundColor !== "transparent");
  }

  /**
   * 生成背景矩形
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateBackgroundRectangle(
    backgroundX: number,
    backgroundY: number,
    backgroundWidth: number,
    backgroundHeight: number,
    style: CaptionStyle
  ): string {
    const bgColor = BaseASSUtils.convertColorToASS(style.backgroundColor);

    // 获取缩放因子
    const scaleX = style.scaleX || 1;
    const scaleY = style.scaleY || 1;

    // 生成圆角矩形路径，考虑缩放因子
    const roundedRectPath = this.generateRoundedRectanglePath(
      backgroundWidth,
      backgroundHeight,
      COMMON_ASS_CONSTANTS.DEFAULT_BORDER_RADIUS,
      scaleX,
      scaleY
    );

    // 使用an7对齐方式（左上角对齐）和pos定位到背景框位置
    return `{\\an7\\pos(${backgroundX},${backgroundY})\\1c${bgColor}\\3c${bgColor}\\p1}${roundedRectPath}{\\p0}`;
  }

  /**
   * 生成圆角矩形路径
   * 使用ASS的绘图功能(\p标签)绘制圆角矩形背景
   */
  private static generateRoundedRectanglePath(
    width: number,
    height: number,
    radius: number = COMMON_ASS_CONSTANTS.DEFAULT_BORDER_RADIUS,
    scaleX: number = 1,
    scaleY: number = 1
  ): string {
    // 根据缩放因子调整圆角半径
    // 使用较小的缩放值来保持圆角比例的协调性
    const scale = Math.min(scaleX, scaleY);
    const adjustedRadius = radius * scale;

    // 确保圆角半径不超过矩形的一半
    const r = Math.min(adjustedRadius, Math.min(width, height) / 2);

    console.log(
      `字幕圆角半径缩放: 原始(${radius}) -> 缩放后(${adjustedRadius}) -> 最终(${r}) [缩放:${scaleX}x${scaleY}, 使用:${scale}x]`
    );

    if (r <= 0) {
      // 如果圆角半径为0或负数，绘制普通矩形
      return `m 0 0 l ${width} 0 l ${width} ${height} l 0 ${height}`;
    }

    // 生成圆角矩形路径
    return [
      `m ${r} 0`,
      `l ${width - r} 0`,
      `b ${width - r} 0 ${width} 0 ${width} ${r}`,
      `l ${width} ${height - r}`,
      `b ${width} ${height - r} ${width} ${height} ${width - r} ${height}`,
      `l ${r} ${height}`,
      `b ${r} ${height} 0 ${height} 0 ${height - r}`,
      `l 0 ${r}`,
      `b 0 ${r} 0 0 ${r} 0`,
    ].join(" ");
  }

  /**
   * 生成完整的ASS文件内容
   */
  static generateASSContent(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!captions?.length) {
      throw new Error("字幕数据不能为空");
    }

    const defaultStyle: CaptionStyle = {
      ...ASS_CONFIG.DEFAULT_STYLE,
      styles: [...ASS_CONFIG.DEFAULT_STYLE.styles],
      gradientColors: [...ASS_CONFIG.DEFAULT_STYLE.gradientColors],
      fontSize: canvasHeight
        ? Math.max(
            ASS_CONFIG.MIN_FONT_SIZE,
            Math.round(canvasHeight * ASS_CONFIG.DEFAULT_FONT_SIZE_RATIO)
          )
        : ASS_CONFIG.DEFAULT_STYLE.fontSize,
    };

    const finalStyle: CaptionStyle = style
      ? { ...defaultStyle, ...style }
      : defaultStyle;

    // 生成ASS文件头部
    let assContent = this.generateASSHeader(canvasWidth, canvasHeight);

    // 添加样式定义
    assContent += `Style: Default,${this.generateASSStyle(
      finalStyle,
      canvasWidth,
      canvasHeight
    )}\n\n`;

    // 添加事件格式
    assContent += `[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`;

    // 处理每个字幕
    captions.forEach((caption) => {
      try {
        const startTime = this.convertTimeToASS(caption.startTime);
        const endTime = this.convertTimeToASS(caption.endTime);

        // 如果需要背景框，先添加背景层
        if (this.shouldRenderBackground(finalStyle)) {
          const backgroundDialogue = this.generateBackgroundDialogue(
            caption,
            finalStyle,
            startTime,
            endTime,
            canvasWidth,
            canvasHeight
          );
          assContent += backgroundDialogue;
        }

        // 添加文字层
        const processedText = this.processSubtitleText(
          caption,
          finalStyle,
          canvasWidth,
          canvasHeight
        );

        assContent += `Dialogue: ${COMMON_ASS_CONSTANTS.TEXT_LAYER},${startTime},${endTime},Default,,0,0,0,,${processedText}\n`;
      } catch (error) {
        console.warn(`处理字幕时出错: ${caption.text}`, error);
      }
    });

    return assContent;
  }

  /**
   * 生成背景对话行
   */
  private static generateBackgroundDialogue(
    caption: Caption,
    style: CaptionStyle,
    startTime: string,
    endTime: string,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    if (!canvasWidth || !canvasHeight) {
      return "";
    }

    // 首先进行自动换行处理，确保背景框尺寸基于换行后的文本
    let processedText = caption.text;
    if (style.width && style.width > 0) {
      processedText = this.wrapTextToWidth(caption.text, style.width, style);
    }

    // 计算背景框尺寸和位置 - 传入换行后的字幕文本以正确计算多行高度
    const { backgroundWidth, backgroundHeight } =
      this.calculateBackgroundDimensions(style, processedText);
    const { xPos: backgroundX, yPos: backgroundY } = this.calculatePosition(
      style,
      canvasWidth,
      canvasHeight,
      processedText
    );

    // 生成背景矩形
    const backgroundRect = this.generateBackgroundRectangle(
      backgroundX,
      backgroundY,
      backgroundWidth,
      backgroundHeight,
      style
    );

    return `Dialogue: ${COMMON_ASS_CONSTANTS.BACKGROUND_LAYER},${startTime},${endTime},Default,,0,0,0,,${backgroundRect}\n`;
  }

  /**
   * 创建ASS字幕文件
   */
  static createASSFile(
    captions: Caption[],
    style?: CaptionStyle,
    canvasWidth?: number,
    canvasHeight?: number
  ): string {
    try {
      const assContent = this.generateASSContent(
        captions,
        style,
        canvasWidth,
        canvasHeight
      );

      const subtitlePath = BaseASSUtils.createTempFile(
        "ass_subtitles",
        assContent
      );
      console.log("Generated ASS subtitle file:", subtitlePath);

      return subtitlePath;
    } catch (error) {
      console.error("创建ASS文件失败:", error);
      throw new Error(
        `创建ASS文件失败: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * 验证ASS文件格式
   */
  static validateASSFile(filePath: string): boolean {
    return BaseASSUtils.validateASSFile(filePath);
  }

  /**
   * 获取ASS文件的字幕数量
   */
  static getSubtitleCount(filePath: string): number {
    return BaseASSUtils.getSubtitleCount(filePath);
  }

  /**
   * 清理临时文件
   */
  static cleanupTempFile(filePath: string): void {
    BaseASSUtils.cleanupTempFile(filePath);
  }
}
