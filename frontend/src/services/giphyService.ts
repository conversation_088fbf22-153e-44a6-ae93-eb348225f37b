import { externalApiClient, handleHttpError } from "../config/httpClient";
import { giphyApiKey } from "../config";
import type { AxiosError } from "axios";

// 添加内容类型枚举
export type ContentType = "gifs" | "stickers" | "clips";

// Giphy API 配置
const GIPHY_API_KEY = giphyApiKey;
const GIPHY_BASE_URL = "https://api.giphy.com/v1";
const PER_PAGE = 20; // 每页GIF数量

// GIF接口定义
export interface GiphyGif {
  id: string;
  title: string;
  url: string;
  images: {
    original: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    fixed_height: {
      url: string;
      width: string;
      height: string;
    };
    fixed_width: {
      url: string;
      width: string;
      height: string;
    };
    preview_gif: {
      url: string;
      width: string;
      height: string;
    };
    downsized: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    downsized_medium: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
  };
  user?: {
    username: string;
    display_name: string;
    avatar_url: string;
  };
  import_datetime: string;
  trending_datetime: string;
  rating: string;
}

// Sticker 接口定义 (类似于 GIF 但可能有些差异)
export interface GiphySticker {
  id: string;
  title: string;
  url: string;
  images: {
    original: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    fixed_height: {
      url: string;
      width: string;
      height: string;
    };
    fixed_width: {
      url: string;
      width: string;
      height: string;
    };
    preview_gif: {
      url: string;
      width: string;
      height: string;
    };
    downsized: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    downsized_medium: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
  };
  user?: {
    username: string;
    display_name: string;
    avatar_url: string;
  };
  import_datetime: string;
  trending_datetime: string;
  rating: string;
}

// Clip 接口定义 (包含视频属性)
export interface GiphyClip {
  id: string;
  title: string;
  url: string;
  embed_url: string;
  duration: string;
  type: string;
  images: {
    original: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    fixed_height: {
      url: string;
      width: string;
      height: string;
    };
    fixed_width: {
      url: string;
      width: string;
      height: string;
    };
    preview_gif: {
      url: string;
      width: string;
      height: string;
    };
    downsized: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
    downsized_medium: {
      url: string;
      width: string;
      height: string;
      size: string;
    };
  };
  video?: {
    assets: {
      [key: string]: {
        url: string;
        width: number;
        height: number;
      };
    };
  };
  user?: {
    username: string;
    display_name: string;
    avatar_url: string;
  };
  import_datetime: string;
  trending_datetime: string;
  rating: string;
}

// 统一的GIF格式
export interface GifFile {
  id: string;
  title: string;
  width: number;
  height: number;
  url: string;
  previewUrl: string;
  thumbnailUrl: string;
  size?: number;
  username?: string;
  rating: string;
  type?: string; // 'gif', 'sticker', 'clip'
  duration?: string; // 仅用于 clips
  videoUrl?: string; // 仅用于 clips
}

// API响应接口
export interface GiphyApiResponse {
  data: (GiphyGif | GiphySticker | GiphyClip)[];
  pagination: {
    total_count: number;
    count: number;
    offset: number;
  };
  meta: {
    status: number;
    msg: string;
    response_id: string;
  };
}

// 统一的API响应格式
export interface GifApiResponse {
  gifs: GifFile[];
  total: number;
  hasMore: boolean;
}

// 将Giphy API响应转换为统一格式
const mapGiphyGifsToCommonFormat = (
  giphyItems: (GiphyGif | GiphySticker | GiphyClip)[],
  contentType: ContentType
): GifFile[] => {
  return giphyItems.map((item) => {
    const baseFormat: GifFile = {
      id: item.id,
      title: item.title || "Untitled",
      width: parseInt(item.images.original.width),
      height: parseInt(item.images.original.height),
      url: item.images.original.url,
      previewUrl: item.images.fixed_height.url,
      thumbnailUrl: item.images.preview_gif.url,
      size: item.images.original.size
        ? parseInt(item.images.original.size)
        : undefined,
      username: item.user?.username,
      rating: item.rating,
      type: contentType,
    };

    // 如果是 clip，添加特殊属性
    if (contentType === "clips" && "duration" in item) {
      const clipItem = item as GiphyClip;
      baseFormat.duration = clipItem.duration;
      baseFormat.videoUrl =
        clipItem.video?.assets?.["480p"]?.url ||
        clipItem.video?.assets?.["360p"]?.url;
    }

    return baseFormat;
  });
};

// 根据内容类型获取对应的端点
const getEndpointForContentType = (contentType: ContentType): string => {
  switch (contentType) {
    case "stickers":
      return `${GIPHY_BASE_URL}/stickers`;
    case "clips":
      return `${GIPHY_BASE_URL}/clips`;
    case "gifs":
    default:
      return `${GIPHY_BASE_URL}/gifs`;
  }
};

// 获取热门内容（支持不同类型）
export const getTrendingContent = async (
  contentType: ContentType = "gifs",
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    const offset = (page - 1) * perPage;
    const endpoint = getEndpointForContentType(contentType);

    const response = await externalApiClient.get<GiphyApiResponse>(
      `${endpoint}/trending`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          limit: perPage,
          offset: offset,
          rating: "g", // 只获取适合所有年龄的内容
        },
      }
    );

    const gifs = mapGiphyGifsToCommonFormat(response.data.data, contentType);
    const total = response.data.pagination.total_count;
    const hasMore = offset + perPage < total;

    return {
      gifs,
      total,
      hasMore,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error(`获取热门${contentType}失败:`, httpError);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 搜索内容（支持不同类型）
export const searchContent = async (
  query: string,
  contentType: ContentType = "gifs",
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    if (!query.trim()) {
      return getTrendingContent(contentType, page, perPage);
    }

    const offset = (page - 1) * perPage;
    const endpoint = getEndpointForContentType(contentType);

    const response = await externalApiClient.get<GiphyApiResponse>(
      `${endpoint}/search`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          q: query,
          limit: perPage,
          offset: offset,
          rating: "g",
          lang: "en",
        },
      }
    );

    const gifs = mapGiphyGifsToCommonFormat(response.data.data, contentType);
    const total = response.data.pagination.total_count;
    const hasMore = offset + perPage < total;

    return {
      gifs,
      total,
      hasMore,
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error(`搜索${contentType}失败:`, httpError);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 保留原有的函数以保持向后兼容性
export const getTrendingGifs = async (
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return getTrendingContent("gifs", page, perPage);
};

export const searchGifs = async (
  query: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return searchContent(query, "gifs", page, perPage);
};

// 根据分类获取内容（支持不同类型）
export const getContentByCategory = async (
  category: string,
  contentType: ContentType = "gifs",
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return searchContent(category, contentType, page, perPage);
};

// 保留原有的函数以保持向后兼容性
export const getGifsByCategory = async (
  category: string,
  page: number = 1,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return searchContent(category, "gifs", page, perPage);
};

// 获取内容分类标签（根据内容类型返回不同标签）
export const getContentCategories = (contentType: ContentType = "gifs") => {
  const baseCategories = {
    popular: [
      "funny",
      "reaction",
      "happy",
      "love",
      "excited",
      "dance",
      "celebrate",
      "thumbs up",
    ],
    emotions: [
      "laugh",
      "cry",
      "angry",
      "surprised",
      "confused",
      "tired",
      "nervous",
      "proud",
    ],
  };

  switch (contentType) {
    case "stickers":
      return {
        ...baseCategories,
        expressions: [
          "hello",
          "goodbye",
          "yes",
          "no",
          "ok",
          "thanks",
          "sorry",
          "wow",
        ],
        characters: ["cute", "cartoon", "emoji", "kawaii", "chibi"],
      };
    case "clips":
      return {
        ...baseCategories,
        actions: ["music", "singing", "talking", "dancing", "sports"],
        themes: ["movie", "tv show", "commercial", "meme", "viral"],
      };
    case "gifs":
    default:
      return {
        ...baseCategories,
        actions: ["wave", "clap", "jump", "run"],
        animals: ["cat", "dog", "bird"],
        nature: ["rain", "snow"],
        technology: ["computer", "phone"],
      };
  }
};

// 保留原有的函数以保持向后兼容性
export const getGifCategories = () => {
  return getContentCategories("gifs");
};

// 获取随机内容（支持不同类型）
export const getRandomContent = async (
  tag?: string,
  contentType: ContentType = "gifs",
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  try {
    const endpoint = getEndpointForContentType(contentType);

    // 随机API只返回一个项目，我们需要多次调用来获取多个
    const promises = Array(perPage)
      .fill(null)
      .map(() =>
        externalApiClient.get<{ data: GiphyGif | GiphySticker | GiphyClip }>(
          `${endpoint}/random`,
          {
            params: {
              api_key: GIPHY_API_KEY,
              tag: tag || "",
              rating: "g",
            },
          }
        )
      );

    const responses = await Promise.all(promises);
    const giphyItems = responses.map((res) => res.data.data);
    const gifs = mapGiphyGifsToCommonFormat(giphyItems, contentType);

    return {
      gifs,
      total: gifs.length,
      hasMore: false, // 随机内容没有分页概念
    };
  } catch (error) {
    const httpError = handleHttpError(error as AxiosError);
    console.error(`获取随机${contentType}失败:`, httpError);
    return { gifs: [], total: 0, hasMore: false };
  }
};

// 保留原有的函数以保持向后兼容性
export const getRandomGifs = async (
  tag?: string,
  perPage: number = PER_PAGE
): Promise<GifApiResponse> => {
  return getRandomContent(tag, "gifs", perPage);
};

// 验证API密钥
export const validateApiKey = async (): Promise<boolean> => {
  try {
    const response = await externalApiClient.get(
      `${GIPHY_BASE_URL}/gifs/trending`,
      {
        params: {
          api_key: GIPHY_API_KEY,
          limit: 1,
        },
      }
    );
    return response.status === 200;
  } catch (error) {
    console.error("Giphy API密钥验证失败:", error);
    return false;
  }
};
