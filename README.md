# Fabric 视频编辑器：全面的基于画布的视频编辑解决方案

Fabric 视频编辑器是一款功能强大的基于 Web 的视频编辑应用程序，它利用 Fabric.js 的功能提供灵活直观的基于画布的编辑体验。该项目结合了 React 前端和 Node.js 后端，提供了全面的视频编辑工具套件，包括时间轴操作、元素管理和动画控制。

该应用程序允许用户通过组合视频、图像、音频和文本等各种媒体元素来创建、编辑和导出视频作品。它具有一套强大的编辑工具，包括位置和变换控制、效果应用和动画功能。

## 仓库结构

该项目主要分为两个目录：`frontend`和`server`。

### 前端

前端使用 React 构建，包含主要的应用程序逻辑和用户界面组件。

- `src/`：包含 React 应用程序的源代码
  - `components/`：各种 UI 元素的 React 组件
  - `editor/`：编辑器特定的组件和逻辑
  - `store/`：使用 MobX 进行状态管理
  - `utils/`：实用函数和辅助工具
  - `types.ts`：TypeScript 类型定义
  - `App.tsx`：主应用程序组件
  - `index.tsx`：React 应用程序的入口点

### 服务器

服务器目录包含后端代码，用于处理视频处理和 FFmpeg 命令生成。

- `src/`：服务器源代码
  - `ffmpeg/`：FFmpeg 命令生成逻辑
  - `index.ts`：主服务器文件

## 使用说明

### 安装

1. 克隆仓库：

   ```
   git clone <仓库URL>
   ```

2. 为前端和服务器安装依赖：

   ```
   cd frontend && npm install
   cd ../server && npm install
   ```

3. 启动开发服务器：
   - 对于前端：
     ```
     cd frontend && npm run dev
     ```
   - 对于后端：
     ```
     cd server && npm run dev
     ```

### 入门指南

1. 在网络浏览器中打开应用程序（通常在`http://localhost:3000`）。
2. 使用左侧边栏将媒体元素（视频、图像、音频）添加到您的作品中。
3. 使用提供的控件在画布上操作元素的位置、缩放和旋转。
4. 使用底部的时间轴调整元素持续时间并添加动画。
5. 使用右侧边栏控件对元素应用效果和样式。
6. 使用顶部菜单中的导出选项导出您的作品。

### 主要功能

- **媒体支持**
  - 支持文本、图像、视频、音频等多种媒体类型
  - 自定义字体和样式设置
  - 音频波形可视化
  - 形状和图形元素
- **画布编辑**
  - 基于画布的直观编辑控件
  - 背景颜色和渐变支持
  - 元素定位、缩放和旋转
  - 图层管理和排序
  - 对齐参考线和吸附功能
- **时间轴和动画**
  - 多轨道时间轴界面
  - 元素持续时间控制
  - 元素间过渡动画
  - 自定义动画效果
- **高级功能**
  - 视频导出与音频同步
  - 视觉滤镜和效果
  - 撤销/重做功能
  - 项目保存/加载功能
  - 字幕和副标题支持

### 故障排除

- 如果您在元素渲染方面遇到问题，请确保您的浏览器支持所需的视频和音频编解码器。
- 对于性能问题，尝试减少作品中的元素数量或简化动画。
- 检查浏览器控制台中的任何错误消息，这些消息可能指示特定问题。
- 如遇到性能相关问题，请参考 [性能优化指南](./docs/PERFORMANCE_OPTIMIZATION.md) 获取详细的优化建议。

## 数据流

1. 用户与 React 前端的 UI 组件交互。
2. 操作触发 MobX 存储（`Store.ts`）中的状态更新。
3. `ElementManager`和`AnimationManager`分别处理元素和动画逻辑。
4. 画布更新通过 Fabric.js 管理，它渲染作品。
5. 对于视频导出，前端将画布状态发送到后端。
6. 后端使用`FFmpegCommandGenerator`生成 FFmpeg 命令。
7. FFmpeg 根据生成的命令处理视频。
8. 处理后的视频发送回前端以供下载。

```
[用户输入] -> [React组件] -> [MobX存储] -> [管理器] -> [Fabric.js画布]
                                       -> [后端API] -> [FFmpeg] -> [处理后的视频]
```

## 基础设施

Fabric 视频编辑器应用程序采用客户端-服务器架构设计，利用现代 Web 技术提供响应迅速且功能强大的视频编辑体验。以下是基础设施的详细分解：

### 前端基础设施

1. **框架**：React.js

   - 提供基于组件的架构来构建用户界面
   - 利用 TypeScript 增强类型安全性和开发者体验

2. **状态管理**：MobX

   - 管理应用程序的状态，包括编辑器元素、动画和 UI 控件
   - 启用响应式编程模式以实现高效更新

3. **画布操作**：Fabric.js

   - 在 HTML5 画布上提供核心编辑功能
   - 处理对象操作、渲染和事件管理

4. **动画**：Anime.js

   - 管理编辑器元素的复杂动画

5. **UI 组件**：Material-UI (MUI)

   - 提供预构建的、可自定义的 React 组件，以实现一致的外观和感觉

6. **开发工具**：
   - Webpack 用于打包和资产管理
   - Babel 用于 JavaScript 转译
   - ESLint 用于代码质量和风格强制执行

### 后端基础设施

1. **服务器**：Node.js 与 Express.js

   - 处理来自前端的 API 请求
   - 管理视频处理任务

2. **视频处理**：FFmpeg

   - 需要安装在服务器上
   - 根据前端的编辑指令处理视频文件

3. **API 端点**：
   - `/api/generateVideo`：接受画布状态并生成视频
   - `/api/progress/:taskId`：提供视频生成任务的进度更新

### 数据库

当前实现不包括数据库。用户项目和资产通过内存和浏览器本地存储进行管理。对于生产环境，考虑实现：

- 数据库系统（例如 PostgreSQL、MongoDB）用于持久存储用户项目和资产
- 用户认证和授权，以安全访问项目

### 部署考虑因素

1. **前端部署**：

   - 可以部署到静态文件托管服务（例如 Netlify、Vercel、带有 CloudFront 的 AWS S3）
   - 需要适当配置路由以支持单页应用程序行为

2. **后端部署**：

   - 需要 Node.js 运行时环境
   - 可以部署到云平台，如 Heroku、AWS Elastic Beanstalk 或 Google Cloud Run
   - 在部署环境中需要安装 FFmpeg

3. **扩展**：
   - 考虑容器化（例如 Docker）以在各环境中实现一致部署
   - 为后端实现负载均衡，以处理多个并发视频处理任务
   - 使用作业队列系统（例如 Redis、RabbitMQ）管理视频处理任务

### 第三方服务和依赖项

- Fabric.js 用于画布操作
- Anime.js 用于动画
- Material-UI 用于 UI 组件
- FFmpeg 用于视频处理（服务器端）

### 性能和可扩展性考虑因素

1. **前端性能**：

   - 实现代码分割和懒加载，以改善初始加载时间
   - 优化大型作品的画布渲染
   - 考虑使用 Web Workers 进行重计算，以防止 UI 阻塞

2. **后端可扩展性**：

   - 实现微服务架构，将视频处理与其他服务器功能分离
   - 使用分布式任务队列在多个工作实例之间管理视频处理作业
   - 实现缓存机制，减少类似视频作品的冗余处理

3. **资产管理**：

   - 利用内容分发网络（CDN）高效传递静态资产和处理后的视频
   - 为用户上传的媒体文件实现高效的存储和检索机制

4. **监控和日志记录**：
   - 为前端和后端实现应用程序性能监控（APM）工具
   - 设置日志记录和错误跟踪服务，以快速识别和解决问题

通过考虑这些基础设施组件和优化，Fabric 视频编辑器可以扩展以处理不断增长的用户群和更复杂的视频编辑任务，同时保持性能和可靠性。

## 🎉 v1.0.0 重大发布

Fabric 视频编辑器 v1.0.0 正式发布！这是一个里程碑版本，包含了完整的视频编辑功能和全面的性能优化：

### 🚀 核心功能

- **完整的视频编辑功能**: 支持视频、图片、音频、文本和形状元素的编辑
- **多轨道时间轴**: 实现了专业级的多轨道时间轴编辑界面
- **动画系统**: 支持多种入场和出场动画效果
- **字幕功能**: 支持手动添加和 AI 自动生成字幕
- **实时预览**: 提供流畅的实时编辑预览体验

### 🔧 技术架构

- **前端**: React 19.1.0 + TypeScript 5.8.3 + MobX 6.13.7 + Fabric.js 5.3.1
- **后端**: Node.js + Express 5.1.0 + FFmpeg 集成
- **UI 框架**: Material-UI 7.2.0 + Anime.js 3.2.2
- **媒体处理**: Wavesurfer.js 7.10.0 音频波形可视化

### 🚀 性能优化

#### 前端优化

- **Store 架构分析**: 完成了单体 Store 分解方案设计
- **组件性能**: 实现了 React.memo 和 useMemo 优化策略
- **时间轴优化**: 提升了大量元素项目的渲染性能
- **媒体处理**: 优化了视频缩略图和音频波形生成

#### 后端优化

- **FFmpeg 优化**: 设计了硬件加速检测和命令优化方案
- **资源管理**: 改进了内存使用和任务队列管理
- **错误处理**: 增强了错误恢复和资源清理机制
- **性能监控**: 实现了详细的性能指标收集系统

### 性能目标

- **时间轴渲染**: 50+ 元素项目 < 2 秒
- **UI 响应性**: 用户交互 < 100ms
- **内存使用**: 典型项目 < 500MB
- **视频处理**: 处理时间减少 30%
- **包大小**: 初始加载 < 2MB
- **首屏加载**: < 3 秒

### 📚 完善的文档体系

#### 技术文档

- **Canvas State 结构文档**: 详细描述了核心数据结构和最佳实践
- **性能优化指南**: 全面的性能优化策略和实施计划
- **API 文档**: 完整的后端 API 接口文档和使用示例
- **前端开发指南**: 详细的前端开发和集成指南

#### 用户文档

- **用户使用指南**: 完整的用户操作手册和功能介绍
- **部署指南**: 详细的生产环境部署说明
- **贡献指南**: 开发者贡献流程和代码规范
- **故障排除**: 常见问题解决方案和调试技巧

### 🌐 第三方集成

#### 媒体资源

- **Jamendo API**: 免费音乐库集成，支持搜索和预览
- **Pexels API**: 高质量图片资源集成
- **Pixabay API**: 多样化媒体资源支持
- **本地上传**: 支持本地媒体文件上传和管理

#### 云服务

- **AWS S3**: 媒体文件存储和管理
- **AWS Transcribe**: AI 语音转文字服务
- **AWS Bedrock**: AI 功能增强支持
- **CDN 支持**: 静态资源分发优化

### 🚀 未来规划

#### 短期目标 (1-3 个月)

#### 性能优化完成项目

基于详细的 [性能优化设计文档](./.kiro/specs/performance-optimization-completion/design.md)，将完成以下核心架构优化：

- **Store 架构重构**: 完成单体 Store 分解为专门的管理器（CanvasStore、ElementStore、TimelineStore、AnimationStore、UIStore），实现细粒度状态管理
- **时间轴虚拟化**: 实现虚拟滚动引擎和智能缓存系统，支持 100+ 元素 60fps 流畅操作
- **硬件加速集成**: 完成硬件能力检测系统和自适应编码策略，FFmpeg 处理速度提升 ≥ 30%
- **媒体处理优化**: 实现智能缩略图生成和音频波形优化，缩略图生成 < 2 秒，波形渲染 < 3 秒
- **内存管理**: 实现智能内存管理系统和资源清理机制，2 小时使用内存增长 < 100MB
- **缓存系统**: 建立多层缓存架构（内存、IndexedDB、服务器），缓存命中率 ≥ 60%
- **并发处理**: 实现优先级任务队列和负载控制，支持 ≥ 5 个并发任务
- **性能监控**: 建立完整的前后端性能监控和告警体系，监控影响 < 1%

#### 功能增强

- **更多动画**: 实现 15+ 种新动画效果
- **模板系统**: 提供预设模板和样式
- **协作功能**: 多用户协作编辑支持
- **云端同步**: 项目云端保存和同步

#### 中期目标 (3-6 个月)

- **移动应用**: 开发移动端应用
- **桌面应用**: Electron 桌面应用
- **插件系统**: 第三方插件支持
- **实时协作**: 实时多人协作编辑

详细信息请参阅 [性能优化指南](./docs/PERFORMANCE_OPTIMIZATION.md) 和 [更新日志](./CHANGELOG.md)。

## Jamendo API 集成

本项目已集成 Jamendo API 用于在线音频库。按照以下步骤配置:

1. 在 [Jamendo 开发者门户](https://devportal.jamendo.com/) 注册账号并创建应用程序
2. 获取 API 密钥
3. 在根目录创建`.env`文件，添加以下内容:
   ```
   REACT_APP_JAMENDO_API_KEY=你的API密钥
   ```
4. 重新启动应用

## 可用的音频源

- 本地音频库
- Jamendo 免费音乐库
  - 支持搜索功能
  - 提供热门曲目浏览
  - **已解决跨域问题**：通过后端代理服务自动处理 CORS 限制

---

## 📚 详细文档

### 用户文档

- [用户使用指南](./docs/USER_GUIDE.md) - 完整的用户操作手册
- [故障排除](./docs/TROUBLESHOOTING.md) - 常见问题解决方案

### 开发者文档

- [API 文档](./docs/API.md) - 完整的后端 API 接口文档
- [前端开发指南](./docs/FRONTEND_GUIDE.md) - 前端开发和集成指南
- [部署指南](./docs/DEPLOYMENT.md) - 生产环境部署说明
- [贡献指南](./docs/CONTRIBUTING.md) - 如何参与项目开发
- [性能优化指南](./docs/PERFORMANCE_OPTIMIZATION.md) - 全面的性能优化策略、监控和测试方案 (v1.0.0)

### 技术文档

- [架构文档](./server/ARCHITECTURE.md) - 系统架构设计
- [Canvas State 结构文档](./docs/canvas-state-structure.md) - 核心数据结构详细说明 (v1.0.0)
- [性能优化指南](./docs/PERFORMANCE_OPTIMIZATION.md) - 全面的性能优化策略和实施计划 (v1.0.0)
- [动画同步实现](./ANIMATION_SYNC_IMPLEMENTATION_SUMMARY.md) - 前后端动画同步机制
- [字幕位置修复](./SUBTITLE_POSITION_FIX.md) - 字幕定位优化方案

### 项目优化

- **[性能优化完成项目需求](./.kiro/specs/performance-optimization-completion/requirements.md)** - 详细的性能优化需求和验收标准 (v1.0.0)
- [优化需求文档](./.kiro/specs/project-optimization/requirements.md) - 详细的优化需求和验收标准
- [优化设计文档](./.kiro/specs/project-optimization/design.md) - 架构优化设计方案
- [更新日志](./CHANGELOG.md) - 项目变更记录和版本历史 (v1.0.0)
