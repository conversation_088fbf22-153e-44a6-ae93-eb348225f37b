import React from "react";
import {
  <PERSON>,
  Tooltip,
  ToggleButton,
  ToggleButtonGroup,
  alpha,
} from "@mui/material";
import PanToolIcon from "@mui/icons-material/PanTool";
import NavigationIcon from "@mui/icons-material/Navigation";
import { useLanguage } from "../../../i18n/LanguageContext";

// 定义编辑模式类型
export type EditMode = "move" | "hand";

interface EditModeToggleProps {
  currentMode: EditMode;
  onModeChange: (mode: EditMode) => void;
  buttonGroupStyles?: React.CSSProperties;
}

export const EditModeToggle: React.FC<EditModeToggleProps> = ({
  currentMode,
  onModeChange,
  buttonGroupStyles,
}) => {
  const { t } = useLanguage();

  const handleEditModeChange = (
    _: React.MouseEvent<HTMLElement>,
    newMode: EditMode | null
  ) => {
    // 如果newMode为null（用户点击当前已选中的按钮），保持当前模式不变
    if (newMode !== null) {
      onModeChange(newMode);
    }
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 0.5,
        height: "100%",
        px: 1,
        ...buttonGroupStyles,
      }}
    >
      <ToggleButtonGroup
        value={currentMode}
        exclusive
        onChange={handleEditModeChange}
        aria-label={t("edit_mode")}
        size="small"
        sx={{
          height: 32,
          "& .MuiToggleButton-root": {
            border: "1px solid",
            borderColor: "divider",
            px: 1.5,
            "&.Mui-selected": {
              backgroundColor: (theme) =>
                theme.palette.mode === "dark"
                  ? alpha(theme.palette.primary.main, 0.2)
                  : alpha(theme.palette.primary.main, 0.1),
              color: "primary.main",
              borderColor: "primary.main",
            },
          },
        }}
      >
        <Tooltip title={`${t("move_mode")} (V)`} arrow>
          <ToggleButton
            value="move"
            aria-label={t("move_mode")}
            tabIndex={0}
            sx={{
              "&:focus-visible": {
                outline: "2px solid",
                outlineColor: "primary.main",
                boxShadow: "0 0 0 4px rgba(25, 118, 210, 0.2)",
              },
            }}
          >
            <NavigationIcon fontSize="small" />
          </ToggleButton>
        </Tooltip>
        <Tooltip title={`${t("hand_tool")} (H)`} arrow>
          <ToggleButton
            value="hand"
            aria-label={t("hand_tool")}
            tabIndex={0}
            sx={{
              "&:focus-visible": {
                outline: "2px solid",
                outlineColor: "primary.main",
                boxShadow: "0 0 0 4px rgba(25, 118, 210, 0.2)",
              },
            }}
          >
            <PanToolIcon fontSize="small" />
          </ToggleButton>
        </Tooltip>
      </ToggleButtonGroup>
    </Box>
  );
};
