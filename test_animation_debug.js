const axios = require("axios");

const canvasState = {
  backgroundColor: "#000000",
  width: 1280,
  height: 720,
  elements: [
    {
      id: "intro_text",
      type: "text",
      name: "Introduction Text",
      placement: {
        x: 297.1,
        y: 380.4,
        width: 800,
        height: 113,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        flipX: false,
        flipY: false,
      },
      timeFrame: {
        start: 1000,
        end: 5000,
      },
      properties: {
        text: "Introducing Our Revolutionary Product",
        fontSize: 50,
        fontWeight: 700,
        fontFamily: "Montserrat",
        fontColor: "#FFFFFF",
        textAlign: "center",
        lineHeight: 1,
        strokeColor: "#000000",
        styles: [],
      },
      opacity: 0.1089,
      trackId: "track2",
      locked: false,
      transition: {
        in: "wipeup",
        inDuration: 0.8,
        out: "fade",
        outDuration: 0.8,
        duration: 0.8,
      },
    },
    {
      id: "feature_text1",
      type: "text",
      name: "Feature Text 1",
      placement: {
        x: 320,
        y: 200,
        width: 500,
        height: 80,
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        flipX: false,
        flipY: false,
      },
      timeFrame: {
        start: 6000,
        end: 9000,
      },
      properties: {
        text: "Innovative Design",
        fontSize: 40,
        fontWeight: 600,
        fontFamily: "Montserrat",
        fontColor: "#FFFFFF",
        textAlign: "left",
      },
      opacity: 1,
      trackId: "track2",
      locked: false,
      transition: {
        in: "wipedown",
        inDuration: 2,
        out: "none",
        duration: 2,
      },
    },
  ],
  animations: [
    {
      id: "intro_text_slide_in",
      targetId: "intro_text",
      type: "slideIn",
      startTime: 1000,
      duration: 800,
      group: "in",
      properties: {
        direction: "bottom",
        useClipPath: true,
      },
    },
    {
      id: "intro_text_fade_out",
      targetId: "intro_text",
      type: "fadeOut",
      startTime: 4200,
      duration: 800,
      group: "out",
      properties: {
        direction: "none",
        useClipPath: false,
      },
    },
    {
      id: "e97jr9e",
      type: "slideIn",
      targetId: "feature_text1",
      duration: 2000,
      group: "in",
      properties: {
        direction: "top",
        useClipPath: true,
      },
    },
  ],
  captions: [],
  globalCaptionStyle: {
    fontSize: 35,
    fontFamily: "Arial",
    fontColor: "#FFFFFF",
    fontWeight: 700,
    textAlign: "center",
    lineHeight: 1.2,
    charSpacing: 0,
    styles: ["bold"],
    strokeWidth: 0.5,
    strokeColor: "#000000",
    shadowColor: "#000000",
    shadowBlur: 2,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    backgroundColor: "transparent",
    useGradient: false,
    gradientColors: ["#FFFFFF", "#000000"],
    positionX: 0,
    positionY: 0,
    originX: "center",
    originY: "bottom",
    scaleX: 1,
    scaleY: 1,
  },
  tracks: [
    {
      id: "track2",
      name: "Text Track",
      type: "text",
      elementIds: ["intro_text", "feature_text1"],
    },
  ],
  canvasScale: 0.45925925925925926,
  canvasTranslation: {
    x: 742,
    y: 312.5,
  },
  outputFormat: {
    codec: "libx264",
    format: "mp4",
    quality: "medium",
    frameRate: 30,
  },
};

async function testAnimationDebug() {
  try {
    console.log("发送测试请求...");
    const response = await axios.post(
      "http://localhost:8080/api/generateVideo",
      canvasState
    );

    console.log("响应状态:", response.status);
    console.log("响应数据:", response.data);
  } catch (error) {
    console.error("请求失败:", error.response?.data || error.message);
  }
}

testAnimationDebug();
