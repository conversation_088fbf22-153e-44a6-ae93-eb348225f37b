import { useCallback, useState, useMemo, useRef } from "react";
import { EditorElement } from "../../../types";
import { formatTime } from "../../../utils/timeUtils";
import {
  checkTimeOverlap,
  findFollowingElements,
  generateSnapPointsFromElements,
  findNearestSnapPoint,
} from "../utils";
import { setGlobalElementDragging } from "../TrackDndContext";

interface UseElementDragProps {
  element: EditorElement;
  allElements: EditorElement[];
  containerWidth: number | null;
  store: any;
}

interface DragHandlers {
  handleLeftHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleRightHandleDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  handleCenterDrag: (event: React.MouseEvent<HTMLDivElement>) => void;
  isDragging: boolean;
  isSnappedStart: boolean;
  isSnappedEnd: boolean;
}

/**
 * 时间线元素拖拽钩子
 * 参考字幕拖拽实现，提供更精确的拖拽体验和视觉反馈
 */
export const useElementDrag = ({
  element,
  allElements,
  containerWidth,
  store,
}: UseElementDragProps): DragHandlers => {
  const [isDragging, setIsDragging] = useState(false);
  const [isSnappedStart, setIsSnappedStart] = useState(false);
  const [isSnappedEnd, setIsSnappedEnd] = useState(false);

  // 使用 useRef 存储拖拽状态，避免闭包问题
  const dragStateRef = useRef({
    initialX: 0,
    initialY: 0,
    initialStartTime: 0,
    initialEndTime: 0,
    dragPreviewActive: false,
    isPanningMode: false,
  });

  // 使用 useRef 缓存 DOM 元素引用
  const elementRefs = useRef({
    leftHandle: null as HTMLElement | null,
    rightHandle: null as HTMLElement | null,
    element: null as HTMLElement | null,
  });

  // 使用 useMemo 缓存吸附点计算结果
  const snapPointsCache = useMemo(() => {
    return generateSnapPointsFromElements(allElements, element.id);
  }, [allElements, element.id]);

  // 统一的碰撞视觉反馈处理函数
  const handleCollisionFeedback = useCallback(
    (
      itemEl: HTMLElement | null,
      isColliding: boolean,
      dragType: "left" | "right" | "center"
    ) => {
      if (!itemEl) return;

      // 使用 CSS transform 代替直接修改位置
      if (isColliding) {
        itemEl.classList.add("panning-mode");
        itemEl.style.transform = "translateX(0)";
      } else {
        itemEl.classList.remove("panning-mode");
      }
    },
    []
  );

  // 更新 DOM 元素引用的函数
  const updateElementRefs = useCallback(() => {
    elementRefs.current = {
      leftHandle: document.getElementById("left-handle"),
      rightHandle: document.getElementById("right-handle"),
      element: document.getElementById(`element-${element.id}`),
    };
  }, [element.id]);

  // 获取媒体元素的原始duration（毫秒）
  const getOriginalDuration = (element: EditorElement): number | null => {
    // 首先检查元素是否有保存的originalDuration属性
    if (element.type === "video" || element.type === "audio") {
      if (element.properties.originalDuration) {
        // 获取播放速度（如果有）
        const playbackSpeed = (element as any).playbackSpeed || 1;
        return element.properties.originalDuration / playbackSpeed;
      }
    }
    return null;
  };

  // 左侧控制柄拖拽处理
  const handleLeftHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      // 更新 DOM 元素引用
      updateElementRefs();

      // 记录当前元素的duration
      const currentDuration = element.timeFrame.end - element.timeFrame.start;

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      // 记录初始位置，用于计算拖拽距离
      const initialPosition = {
        x: event.clientX,
        y: event.clientY,
      };

      // 添加拖拽预览效果的状态
      let dragPreviewActive = false;
      let dragDistance = 0;

      const initialStartTime = element.timeFrame.start;
      // 获取当前元素左边的所有元素
      const precedingElements = allElements
        .filter((e) => e.id !== element.id)
        .filter((e) => e.timeFrame.end <= initialStartTime)
        .map((e) => ({
          id: e.id,
          startTime: e.timeFrame.start,
          endTime: e.timeFrame.end,
          offsetFromCurrent: initialStartTime - e.timeFrame.end,
        }))
        .sort((a, b) => b.endTime - a.endTime);

      // 找到最近的左边元素
      const prevElement =
        precedingElements.length > 0
          ? precedingElements.reduce((nearest, current) => {
              return nearest.offsetFromCurrent < current.offsetFromCurrent
                ? nearest
                : current;
            })
          : null;

      let isPanningMode = false;
      let lastUpdateTime = 0; // 用于节流控制

      // 获取媒体元素的原始duration
      const originalDuration = getOriginalDuration(element);

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 使用节流来减少更新频率，提高性能
        const now = performance.now();
        if (now - lastUpdateTime < 16) return; // 限制为60fps
        lastUpdateTime = now;

        // 使用 requestAnimationFrame 来优化更新
        requestAnimationFrame(() => {
          // 计算拖拽距离
          dragDistance = Math.sqrt(
            Math.pow(moveEvent.clientX - initialPosition.x, 2) +
              Math.pow(moveEvent.clientY - initialPosition.y, 2)
          );

          // 只有当拖拽距离超过阈值时才开始拖拽
          if (!dragPreviewActive && dragDistance > 5) {
            dragPreviewActive = true;
            elementRefs.current.leftHandle?.classList.add("handle-dragging");
            elementRefs.current.element?.classList.add("resize-active");
          }

          const trackRect = trackElement.getBoundingClientRect();
          const trackX = moveEvent.clientX - trackRect.left;
          const trackPercent = Math.max(
            0,
            Math.min(1, trackX / trackRect.width)
          );

          const visibleStartTime = store.timelinePan.offsetX;
          const newStartTime =
            visibleStartTime + trackPercent * store.timelineDisplayDuration;

          let safeStartTime: number;
          if (currentDuration <= 1000) {
            // 如果当前duration小于等于1秒，只允许向右拖动（减少startTime，增加duration）
            if (newStartTime < initialStartTime) {
              safeStartTime = newStartTime; // 允许向右拖动
            } else {
              safeStartTime = initialStartTime; // 不允许向左拖动
            }
          } else {
            // 正常情况，确保拖拽后的duration不小于1秒
            safeStartTime = Math.min(
              newStartTime,
              element.timeFrame.end - 1000
            );

            // 对于视频和音频元素，确保duration不超过媒体实际duration
            if (
              (element.type === "video" || element.type === "audio") &&
              originalDuration !== null
            ) {
              // 计算当前拖拽后的duration
              const newDuration = element.timeFrame.end - safeStartTime;

              // 如果拖拽后的duration超过媒体实际duration，则限制startTime
              if (newDuration > originalDuration) {
                // 确保不会小于0
                safeStartTime = Math.max(
                  0,
                  element.timeFrame.end - originalDuration
                );
              }
            }
          }

          // 使用缓存的吸附点
          const snapResult = findNearestSnapPoint(
            safeStartTime,
            snapPointsCache
          );

          const newIsSnapped = snapResult.snapped;

          if (newIsSnapped) {
            safeStartTime = snapResult.time;
          }

          if (newIsSnapped !== isSnappedStart) {
            setIsSnappedStart(newIsSnapped);
            if (newIsSnapped) {
              elementRefs.current.leftHandle?.classList.add("snapped");
            } else {
              elementRefs.current.leftHandle?.classList.remove("snapped");
            }
          }

          // 检测拖动方向，只有向左拖动时才考虑进入平移模式
          const isDraggingLeft = safeStartTime < initialStartTime;
          const isColliding =
            prevElement &&
            isDraggingLeft &&
            (safeStartTime <= prevElement.endTime ||
              Math.abs(safeStartTime - prevElement.endTime) < 1);

          // 只在向左拖动并且碰撞时进入平移模式
          isPanningMode = isColliding;

          // 更新平移模式的视觉反馈
          handleCollisionFeedback(
            elementRefs.current.element,
            isPanningMode,
            "left"
          );

          if (!isPanningMode) {
            // 不在平移模式，只更新当前元素的startTime
            store.updateEditorElementTimeFrame(
              element,
              {
                start: Math.round(safeStartTime),
                end: element.timeFrame.end,
              },
              false
            ); // 明确标记为非拖拽结束
          } else {
            // 在平移模式（向左拖动碰撞），移动当前元素和前面的元素
            const timeDelta = safeStartTime - initialStartTime;

            // 检查最左边的元素是否会超出时间轴起始位置
            const leftmostElement =
              precedingElements[precedingElements.length - 1];
            if (leftmostElement) {
              const newLeftmostStart = leftmostElement.startTime + timeDelta;
              if (newLeftmostStart < 0) {
                return; // 如果会超出，则不允许继续拖动
              }
            }

            // 更新当前元素的位置
            store.updateEditorElementTimeFrame(
              element,
              {
                start: Math.round(safeStartTime),
                end: element.timeFrame.end,
              },
              false
            ); // 明确标记为非拖拽结束

            // 更新所有前面元素的位置
            precedingElements.forEach(
              ({ id, startTime, endTime, offsetFromCurrent }, index) => {
                let newEnd: number;
                if (index === 0) {
                  newEnd = safeStartTime; // 第一个元素直接连接到当前元素
                } else {
                  newEnd = safeStartTime - offsetFromCurrent; // 其他元素保持相对距离
                }
                const duration = endTime - startTime;
                const newStart = newEnd - duration;

                const elementToUpdate = allElements.find((e) => e.id === id);
                if (elementToUpdate) {
                  store.updateEditorElementTimeFrame(
                    elementToUpdate,
                    {
                      start: Math.round(newStart),
                      end: Math.round(newEnd),
                    },
                    false
                  ); // 明确标记为非拖拽结束
                }
              }
            );
          }
        });
      };

      const handleMouseUp = () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
        setIsDragging(false);
        setGlobalElementDragging(false); // 重置全局拖拽状态
        setIsSnappedStart(false);
        isPanningMode = false;

        // 清除所有类
        elementRefs.current.leftHandle?.classList.remove(
          "handle-dragging",
          "snapped"
        );
        elementRefs.current.element?.classList.remove(
          "resize-active",
          "panning-mode",
          "dragging-active"
        );

        // 对于视频和音频元素，更新mediaStartTime
        if (element.type === "video" || element.type === "audio") {
          // 获取当前元素的最新状态
          const updatedElement = store.editorElements.find(
            (el) => el.id === element.id
          );
          if (updatedElement) {
            // 计算拖拽的时间差（秒）
            const timeDelta =
              (updatedElement.timeFrame.start - initialStartTime) / 1000;

            // 获取当前的mediaStartTime（如果有）
            const currentMediaStartTime =
              (updatedElement.properties as any).mediaStartTime || 0;

            // 计算新的mediaStartTime
            const newMediaStartTime = Math.max(
              0,
              currentMediaStartTime + timeDelta
            );

            // 更新元素的mediaStartTime
            (updatedElement.properties as any).mediaStartTime =
              newMediaStartTime;
          }
        }

        // 拖拽结束后进行轨道内元素的重叠修复
        const track = store.trackManager.getTrackByElementId(element.id);
        if (track) {
          store.trackManager.fixTrackElementsOverlap(track.id);
        }

        // 选中当前元素
        store.setSelectedElement(element);

        // 检查并删除所有空轨道
        store.trackManager.removeEmptyTracks();

        // 根据轨道顺序更新Canvas上的元素显示顺序
        // 确保拖拽后canvas层级与timeline轨道顺序保持一致
        store.updateCanvasOrderByTrackOrder();

        // 更新最大时间
        store.updateMaxTime();

        // 保存更改
        store.saveChange("move_element");
      };

      setIsDragging(true);
      setGlobalElementDragging(true); // 设置全局拖拽状态
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      element,
      containerWidth,
      isDragging,
      store,
      allElements,
      getOriginalDuration,
      isSnappedStart,
      snapPointsCache,
      handleCollisionFeedback,
      updateElementRefs,
    ]
  );

  // 右侧控制柄拖拽处理
  const handleRightHandleDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      // 更新 DOM 元素引用
      updateElementRefs();

      // 初始化拖拽状态
      dragStateRef.current = {
        initialX: event.clientX,
        initialY: event.clientY,
        initialStartTime: element.timeFrame.start,
        initialEndTime: element.timeFrame.end,
        dragPreviewActive: false,
        isPanningMode: false,
      };

      const trackElement =
        document.querySelector(".timeline-container") ||
        event.currentTarget.parentElement;
      if (!trackElement) return;

      // 记录当前元素的duration
      const currentDuration = element.timeFrame.end - element.timeFrame.start;

      // 获取当前元素右边的所有元素
      const followingElements = allElements
        .filter((e) => e.id !== element.id)
        .filter((e) => e.timeFrame.start >= element.timeFrame.end)
        .map((e) => ({
          id: e.id,
          startTime: e.timeFrame.start,
          endTime: e.timeFrame.end,
          offsetFromCurrent: e.timeFrame.start - element.timeFrame.end,
        }))
        .sort((a, b) => a.startTime - b.startTime);

      // 找到最近的右边元素
      const nextElement =
        followingElements.length > 0
          ? followingElements.reduce((nearest, current) => {
              return nearest.offsetFromCurrent < current.offsetFromCurrent
                ? nearest
                : current;
            })
          : null;

      let isPanningMode = false;
      let lastUpdateTime = 0; // 用于节流控制

      // 获取媒体元素的原始duration
      const originalDuration = getOriginalDuration(element);

      // 使用 requestAnimationFrame 优化动画性能
      let animationFrameId: number;

      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 使用节流来减少更新频率，提高性能
        const now = performance.now();
        if (now - lastUpdateTime < 16) return; // 限制为60fps
        lastUpdateTime = now;

        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }

        animationFrameId = requestAnimationFrame(() => {
          const { initialX, initialY, initialEndTime } = dragStateRef.current;

          // 计算拖拽距离
          const dragDistance = Math.hypot(
            moveEvent.clientX - initialX,
            moveEvent.clientY - initialY
          );

          // 只有当拖拽距离超过阈值时才开始拖拽
          if (!dragStateRef.current.dragPreviewActive && dragDistance > 5) {
            dragStateRef.current.dragPreviewActive = true;
            elementRefs.current.rightHandle?.classList.add("handle-dragging");
            elementRefs.current.element?.classList.add("resize-active");
          }

          const trackRect = trackElement.getBoundingClientRect();
          const trackX = moveEvent.clientX - trackRect.left;
          const trackPercent = Math.max(
            0,
            Math.min(1, trackX / trackRect.width)
          );

          const visibleStartTime = store.timelinePan.offsetX;
          const newEndTime =
            visibleStartTime + trackPercent * store.timelineDisplayDuration;

          // 确保拖拽后的duration不小于1秒
          let safeEndTime = Math.max(
            element.timeFrame.start + 1000,
            newEndTime
          );

          // 对于视频和音频元素，确保duration不超过媒体实际可用duration
          if (
            (element.type === "video" || element.type === "audio") &&
            originalDuration !== null
          ) {
            // 获取当前的mediaStartTime（如果有）
            const mediaStartTime =
              (element.properties as any).mediaStartTime || 0;

            // 计算媒体的剩余可用时长（原始时长减去已经跳过的部分）
            const remainingDuration = originalDuration - mediaStartTime * 1000;

            // 计算当前拖拽后的duration
            const newDuration = safeEndTime - element.timeFrame.start;

            // 如果拖拽后的duration超过媒体剩余可用duration，则限制endTime
            if (newDuration > remainingDuration) {
              safeEndTime = element.timeFrame.start + remainingDuration;
              console.log(
                `限制元素 ${element.id} 的endTime: mediaStartTime=${mediaStartTime}秒, 剩余可用时长=${remainingDuration}毫秒`
              );
            }
          }

          // 使用缓存的吸附点
          const snapResult = findNearestSnapPoint(safeEndTime, snapPointsCache);

          const newIsSnapped = snapResult.snapped;

          if (newIsSnapped) {
            safeEndTime = snapResult.time;
          }

          // 仅在吸附状态改变时更新状态和类名
          if (newIsSnapped !== isSnappedEnd) {
            setIsSnappedEnd(newIsSnapped);
            if (newIsSnapped) {
              elementRefs.current.rightHandle?.classList.add("snapped");
            } else {
              elementRefs.current.rightHandle?.classList.remove("snapped");
            }
          }

          // 检测拖动方向，只有向右拖动时才考虑进入平移模式
          const isDraggingRight = safeEndTime > initialEndTime;
          const isColliding =
            nextElement &&
            isDraggingRight &&
            (safeEndTime >= nextElement.startTime ||
              Math.abs(safeEndTime - nextElement.startTime) < 1);

          // 只在向右拖动并且碰撞时进入平移模式
          isPanningMode = isColliding;

          // 更新平移模式的视觉反馈
          handleCollisionFeedback(
            elementRefs.current.element,
            isPanningMode,
            "right"
          );

          if (!isPanningMode) {
            // 不在平移模式，只更新当前元素的endTime
            store.updateEditorElementTimeFrame(
              element,
              {
                start: element.timeFrame.start,
                end: Math.round(safeEndTime),
              },
              false
            ); // 明确标记为非拖拽结束
          } else {
            // 在平移模式（向右拖动碰撞），移动当前元素和右边的元素
            const timeDelta = safeEndTime - initialEndTime;

            // 更新当前元素的位置
            store.updateEditorElementTimeFrame(
              element,
              {
                start: element.timeFrame.start,
                end: Math.round(safeEndTime),
              },
              false
            ); // 明确标记为非拖拽结束

            // 更新所有右边元素的位置
            followingElements.forEach(
              ({ id, startTime, endTime, offsetFromCurrent }, index) => {
                let newStart: number;
                if (index === 0) {
                  newStart = safeEndTime; // 第一个元素直接连接到当前元素
                } else {
                  newStart = safeEndTime + offsetFromCurrent; // 其他元素保持相对距离
                }
                const duration = endTime - startTime;
                const newEnd = newStart + duration;

                const elementToUpdate = allElements.find((e) => e.id === id);
                if (elementToUpdate) {
                  store.updateEditorElementTimeFrame(
                    elementToUpdate,
                    {
                      start: Math.round(newStart),
                      end: Math.round(newEnd),
                    },
                    false
                  ); // 明确标记为非拖拽结束
                }
              }
            );
          }
        });
      };

      const handleMouseUp = () => {
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }

        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);

        setIsDragging(false);
        setGlobalElementDragging(false); // 重置全局拖拽状态
        setIsSnappedEnd(false);
        isPanningMode = false;

        // 清除所有类
        elementRefs.current.rightHandle?.classList.remove(
          "handle-dragging",
          "snapped"
        );
        elementRefs.current.element?.classList.remove(
          "resize-active",
          "panning-mode"
        );

        // 修复轨道重叠
        const track = store.trackManager.getTrackByElementId(element.id);
        if (track) {
          store.trackManager.fixTrackElementsOverlap(track.id);
        }

        // 选中当前元素
        store.setSelectedElement(element);

        // 检查并删除所有空轨道
        store.trackManager.removeEmptyTracks();

        // 根据轨道顺序更新Canvas上的元素显示顺序
        // 确保拖拽后canvas层级与timeline轨道顺序保持一致
        store.updateCanvasOrderByTrackOrder();

        // 更新最大时间
        store.updateMaxTime();

        // 保存更改
        store.saveChange("move_element");
      };

      setIsDragging(true);
      setGlobalElementDragging(true); // 设置全局拖拽状态
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      element,
      containerWidth,
      isDragging,
      store,
      snapPointsCache,
      allElements,
      getOriginalDuration,
      isSnappedEnd,
      handleCollisionFeedback,
      updateElementRefs,
    ]
  );

  // 中间拖拽处理
  const handleCenterDrag = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (isDragging || !containerWidth) return;

      // 更新 DOM 元素引用 - 只在开始时获取一次
      updateElementRefs();

      // 防止事件冒泡和默认行为
      event.stopPropagation();
      event.preventDefault();

      // 获取轨道容器元素
      const trackElement =
        event.currentTarget?.closest(".timeline-container") ||
        document.querySelector(".timeline-container");

      if (!trackElement) {
        console.warn("无法找到轨道容器元素");
        return;
      }

      // 预先计算和缓存常量值，避免重复计算
      const trackRect = trackElement.getBoundingClientRect();
      const pixelsPerMs = trackRect.width / store.timelineDisplayDuration;
      const elementEl = elementRefs.current.element;

      // 记录初始状态
      const initialState = {
        x: event.clientX,
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        duration: element.timeFrame.end - element.timeFrame.start,
      };

      // 记录拖动方向
      let dragDirection: "left" | "right" | "none" = "none";
      let lastUpdateTime = 0;
      let lastDeltaX = 0;
      let lastStartTime = initialState.startTime;
      let lastEndTime = initialState.endTime;
      let animationFrameId: number | null = null;

      // 添加拖拽样式 - 只在开始时设置一次，避免频繁DOM操作
      if (elementEl) {
        dragStateRef.current.dragPreviewActive = true;
        elementEl.classList.add("dragging-active");
        elementEl.style.transition = "none";
        elementEl.style.willChange = "transform";
      }

      // 使用高效的事件处理
      const handleMouseMove = (moveEvent: MouseEvent) => {
        // 计算移动距离
        const deltaX = moveEvent.clientX - initialState.x;

        // 如果移动距离太小，不处理
        if (Math.abs(deltaX - lastDeltaX) < 0.5) return;

        // 当前时间
        const now = performance.now();

        // 如果距离上次更新时间太短，使用RAF节流
        if (now - lastUpdateTime < 16) {
          // 约60fps
          if (animationFrameId !== null) {
            cancelAnimationFrame(animationFrameId);
          }

          animationFrameId = requestAnimationFrame(() =>
            processMove(deltaX, now)
          );
          return;
        }

        // 直接处理移动
        processMove(deltaX, now);
      };

      // 将移动处理逻辑提取为函数，减少重复代码
      const processMove = (deltaX: number, now: number) => {
        // 更新最后处理时间
        lastUpdateTime = now;
        lastDeltaX = deltaX;

        // 确定拖动方向
        dragDirection = deltaX < 0 ? "left" : deltaX > 0 ? "right" : "none";

        // 计算新的时间位置 - 直接使用整数计算，避免浮点运算
        const timeDelta = Math.round(deltaX / pixelsPerMs);
        let newStartTime = Math.max(0, initialState.startTime + timeDelta);
        let newEndTime = newStartTime + initialState.duration;

        // 如果位置没有变化，不进行更新
        if (newStartTime === lastStartTime && newEndTime === lastEndTime)
          return;

        lastStartTime = newStartTime;
        lastEndTime = newEndTime;

        // 使用缓存的吸附点
        let finalStartTime = newStartTime;
        let finalEndTime = newEndTime;
        let isStartSnapped = false;
        let isEndSnapped = false;

        // 只在需要时进行吸附点计算
        if (Math.abs(deltaX) % 5 === 0) {
          // 每5个像素检查一次吸附点
          const startSnapResult = findNearestSnapPoint(
            newStartTime,
            snapPointsCache
          );
          const endSnapResult = findNearestSnapPoint(
            newEndTime,
            snapPointsCache
          );

          if (startSnapResult.snapped) {
            finalStartTime = startSnapResult.time;
            finalEndTime = finalStartTime + initialState.duration;
            isStartSnapped = true;
          }

          if (endSnapResult.snapped) {
            finalEndTime = endSnapResult.time;
            finalStartTime = finalEndTime - initialState.duration;
            isEndSnapped = true;
          }
        }

        // 批量更新状态，减少重渲染
        if (isStartSnapped !== isSnappedStart) {
          setIsSnappedStart(isStartSnapped);

          // 避免频繁DOM操作，使用类名标记状态
          if (elementEl) {
            if (isStartSnapped) {
              elementEl.classList.add("snapped-start");
            } else {
              elementEl.classList.remove("snapped-start");
            }
          }
        }

        if (isEndSnapped !== isSnappedEnd) {
          setIsSnappedEnd(isEndSnapped);

          if (elementEl) {
            if (isEndSnapped) {
              elementEl.classList.add("snapped-end");
            } else {
              elementEl.classList.remove("snapped-end");
            }
          }
        }

        // 直接更新元素位置，避免不必要的重渲染
        // 使用isDraggingTimeFrame标记来减少重渲染
        if (!store.isDraggingTimeFrame) {
          store.setDraggingTimeFrameStart();
        }

        store.updateEditorElementTimeFrame(
          element,
          {
            start: Math.round(finalStartTime),
            end: Math.round(finalEndTime),
          },
          false
        );
      };

      const handleMouseUp = () => {
        // 清理动画帧
        if (animationFrameId !== null) {
          cancelAnimationFrame(animationFrameId);
        }

        // 移除事件监听
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);

        // 重置状态
        setIsDragging(false);
        setGlobalElementDragging(false); // 重置全局拖拽状态
        setIsSnappedStart(false);
        setIsSnappedEnd(false);

        // 标记拖拽结束
        if (store.isDraggingTimeFrame) {
          store.setDraggingTimeFrameEnd();
        }

        // 清除所有类和样式
        if (elementEl) {
          elementEl.classList.remove(
            "dragging-active",
            "resize-active",
            "panning-mode",
            "snapped-start",
            "snapped-end"
          );

          // 移除直接设置的样式
          elementEl.style.transition = "";
          elementEl.style.willChange = "";
        }

        // 获取当前元素状态
        const currentElement = allElements.find((e) => e.id === element.id);
        if (!currentElement) return;

        // 获取当前元素的时间范围
        const currentStartTime = currentElement.timeFrame.start;
        const currentEndTime = currentElement.timeFrame.end;

        // 优化碰撞检测：只在需要时执行
        if (dragDirection !== "none") {
          const otherElements = allElements.filter((e) => e.id !== element.id);

          // 根据拖动方向优化碰撞检测
          const leftElements =
            dragDirection === "left"
              ? otherElements
                  .filter((e) => e.timeFrame.end <= currentStartTime + 5)
                  .sort((a, b) => b.timeFrame.end - a.timeFrame.end)
              : [];

          const rightElements =
            dragDirection === "right"
              ? otherElements
                  .filter((e) => e.timeFrame.start >= currentEndTime - 5)
                  .sort((a, b) => a.timeFrame.start - b.timeFrame.start)
              : [];

          // 找到最近的左右元素
          const leftElement = leftElements.length > 0 ? leftElements[0] : null;
          const rightElement =
            rightElements.length > 0 ? rightElements[0] : null;

          // 处理碰撞对齐
          let updatedStartTime = currentStartTime;
          let updatedEndTime = currentEndTime;
          let needUpdateCurrentElement = false;

          // 检测碰撞并处理
          const currentDuration = currentEndTime - currentStartTime;

          // 处理左侧碰撞
          if (
            leftElement &&
            Math.abs(currentStartTime - leftElement.timeFrame.end) < 5
          ) {
            updatedStartTime = leftElement.timeFrame.end;

            // 保持持续时间不变
            if (dragDirection === "left") {
              updatedEndTime = updatedStartTime + currentDuration;
              needUpdateCurrentElement = true;
            }
          }

          // 处理右侧碰撞
          if (
            rightElement &&
            Math.abs(currentEndTime - rightElement.timeFrame.start) < 5
          ) {
            updatedEndTime = rightElement.timeFrame.start;

            // 如果是向右拖动，保持持续时间不变
            if (dragDirection === "right" && !needUpdateCurrentElement) {
              needUpdateCurrentElement = true;
            }
          }

          // 更新当前元素
          if (needUpdateCurrentElement) {
            store.updateEditorElementTimeFrame(
              currentElement,
              {
                start: Math.round(updatedStartTime),
                end: Math.round(updatedEndTime),
              },
              true // 标记为拖拽结束
            );
          } else {
            // 如果没有碰撞调整，也要标记为拖拽结束
            store.updateEditorElementTimeFrame(
              currentElement,
              {
                start: currentStartTime,
                end: currentEndTime,
              },
              true
            );
          }
        } else {
          // 即使没有拖动，也需要标记为拖拽结束
          store.updateEditorElementTimeFrame(
            currentElement,
            {
              start: currentStartTime,
              end: currentEndTime,
            },
            true
          );
        }

        // 选中当前元素
        store.setSelectedElement(element);

        // 延迟执行轨道重叠修复，确保不影响精确对齐
        const track = store.trackManager.getTrackByElementId(element.id);
        if (track) {
          // 使用requestAnimationFrame代替setTimeout，更加流畅
          requestAnimationFrame(() => {
            store.trackManager.fixTrackElementsOverlap(track.id);
            store.trackManager.removeEmptyTracks();
            // 根据轨道顺序更新Canvas上的元素显示顺序
            // 确保拖拽后canvas层级与timeline轨道顺序保持一致
            store.updateCanvasOrderByTrackOrder();
            // 更新最大时间
            store.updateMaxTime();
            store.saveChange("move_element");
          });
        } else {
          // 根据轨道顺序更新Canvas上的元素显示顺序
          store.updateCanvasOrderByTrackOrder();
          // 更新最大时间
          store.updateMaxTime();
          store.saveChange("move_element");
        }
      };

      // 设置拖拽状态
      setIsDragging(true);
      setGlobalElementDragging(true); // 设置全局拖拽状态

      // 使用passive事件监听提高性能
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      // 如果正在播放，暂停播放
      if (store.playing) {
        store.setPlaying(false);
      }
    },
    [
      element,
      containerWidth,
      isDragging,
      store,
      snapPointsCache,
      allElements,
      updateElementRefs,
    ]
  );

  return {
    handleLeftHandleDrag,
    handleRightHandleDrag,
    handleCenterDrag,
    isDragging,
    isSnappedStart,
    isSnappedEnd,
  };
};
