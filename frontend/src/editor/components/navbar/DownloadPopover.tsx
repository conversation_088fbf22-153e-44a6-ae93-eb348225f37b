import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useRef,
  useMemo,
  useState,
} from "react";
import {
  Box,
  Button,
  IconButton,
  LinearProgress,
  Tooltip,
  Typography,
} from "@mui/material";
import DownloadIcon from "@mui/icons-material/Download";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import { StoreContext } from "../../../store";
import { CustomPopover } from "..";
import { useLanguage } from "../../../i18n/LanguageContext";
import { apiBaseUrl } from "../../../config";

// Constants
const API_BASE_URL = `${apiBaseUrl}/api`;
const PROGRESS_CHECK_INTERVAL = 1000; // 1 second

// Common styles
const COMMON_STYLES = {
  flexColumnWithGap: {
    display: "flex",
    flexDirection: "column",
    gap: 2,
    padding: 2,
  },
};

// Define a function to display friendly stage names
const getStageDisplayName = (
  stage: string,
  t: (key: string) => string
): string => {
  const stageMap: Record<string, string> = {
    initializing: t("initializing"),
    detecting_audio: t("detecting_audio"),
    detecting_audio_elements: t("detecting_audio_elements"),
    processing_elements: t("processing_elements"),
    processing_audio_elements: t("processing_audio_elements"),
    processing_visual_elements: t("processing_visual_elements"),
    processing_captions: t("processing_captions"),
    building_command: t("building_command"),
    generating_command: t("generating_command"),
    rendering: t("rendering"),
    finalizing: t("finalizing"),
    completed: t("completed"),
    failed: t("failed"),
    cancelled: t("cancelled"),
  };

  return stageMap[stage] || stage;
};

interface IDownloadState {
  renderId: string;
  progress: number;
  isDownloading: boolean;
  status?: "pending" | "processing" | "completed" | "failed" | "cancelled";
  stage?: string;
  error?: string;
  downloadUrl?: string;
}

const INITIAL_DOWNLOAD_STATE: IDownloadState = {
  progress: 0,
  isDownloading: false,
  renderId: "",
  status: undefined,
  error: undefined,
  downloadUrl: undefined,
};

// 定义导出格式选项
const FORMAT_OPTIONS = [
  { value: "mp4", label: "format_mp4" },
  // { value: "gif", label: "format_gif" },
  { value: "mov", label: "format_mov" },
  { value: "mp3", label: "format_mp3" },
];

// 定义视频质量选项
const QUALITY_OPTIONS = [
  { value: "high", label: "quality_high", description: "quality_high_desc" },
  {
    value: "medium",
    label: "quality_medium",
    description: "quality_medium_desc",
  },
  { value: "low", label: "quality_low", description: "quality_low_desc" },
];

export const DownloadPopover = React.memo(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [downloadState, setDownloadState] = useState<IDownloadState>(
    INITIAL_DOWNLOAD_STATE
  );
  const [exportFormat, setExportFormat] = useState("mp4");
  const [exportQuality, setExportQuality] = useState("medium");
  const [isExporting, setIsExporting] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const resetDownloadState = useCallback(() => {
    setDownloadState(INITIAL_DOWNLOAD_STATE);
  }, []);

  const handleExport = useCallback(async () => {
    try {
      setIsExporting(true);
      setDownloadState((prev) => ({
        ...prev,
        isDownloading: true,
        progress: 0,
      }));
      console.log("downloading");
      const data = await store.exportVideo(exportFormat, exportQuality);

      setDownloadState((prev) => ({
        ...prev,
        renderId: data.taskId,
      }));
    } catch (error) {
      console.error("Export failed:", error);
      resetDownloadState();
      // Add error notification here
      alert(t("generation_failed"));
    } finally {
      setIsExporting(false);
    }
  }, [exportFormat, exportQuality, resetDownloadState, store, t]);

  // 查询进度的逻辑
  useEffect(() => {
    if (downloadState.renderId && downloadState.isDownloading) {
      intervalRef.current = setInterval(async () => {
        try {
          const progressUrl = `${API_BASE_URL}/progress/${downloadState.renderId}`;
          console.log("Fetching progress from:", progressUrl);

          const response = await fetch(progressUrl);

          if (!response.ok) {
            throw new Error(`Failed to fetch progress: ${response.status}`);
          }

          const data = await response.json();
          console.log("Progress data:", data);

          if (data.status === "completed") {
            // 生成下载URL
            const downloadUrl = `${API_BASE_URL}/download/${downloadState.renderId}`;
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              progress: 100,
              stage: data.stage || "completed",
              downloadUrl,
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.status === "failed") {
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              stage: data.stage || "failed",
              error: data.error || t("generation_failed"),
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.status === "cancelled") {
            setDownloadState((prev) => ({
              ...prev,
              status: data.status,
              stage: data.stage || "cancelled",
              error: data.error || t("cancelled"),
            }));
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
          } else if (data.progress !== undefined) {
            // 区分处理中状态和最终处理状态
            setDownloadState((prev) => {
              // 只有当新进度大于当前进度时才更新
              if (data.progress > prev.progress || data.stage !== prev.stage) {
                return {
                  ...prev,
                  status: data.status,
                  progress: data.progress,
                  stage: data.stage,
                };
              }
              return prev;
            });
          }
        } catch (error) {
          console.error("Failed to fetch progress:", error);
        }
      }, PROGRESS_CHECK_INTERVAL);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [downloadState.renderId, downloadState.isDownloading, t]);

  // 取消视频处理的逻辑
  const cancelVideoProcessing = useCallback(
    async (renderId: string) => {
      if (!renderId) return;

      try {
        const response = await fetch(`${API_BASE_URL}/cancel/${renderId}`, {
          method: "POST",
        });

        if (!response.ok) {
          throw new Error(`Failed to cancel task: ${response.status}`);
        }

        console.log("Task cancelled successfully");
      } catch (error) {
        console.error("Error cancelling task:", error);
      } finally {
        // Clean up frontend resources regardless of server response
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        resetDownloadState();
      }
    },
    [resetDownloadState]
  );

  // 处理下载视频后的操作
  const handleDownloadVideo = useCallback(() => {
    // 恢复初始状态
    resetDownloadState();
  }, [resetDownloadState]);

  // 获取当前选择的格式标签
  const currentFormatLabel = useMemo(() => {
    const option = FORMAT_OPTIONS.find((opt) => opt.value === exportFormat);
    return option ? t(option.label) : t("format_mp4");
  }, [exportFormat, t]);

  // 获取当前选择的质量标签
  const currentQualityLabel = useMemo(() => {
    const option = QUALITY_OPTIONS.find((opt) => opt.value === exportQuality);
    return option ? t(option.label) : t("quality_medium");
  }, [exportQuality, t]);

  // 处理格式选择
  const handleFormatSelect = useCallback((format: string) => {
    setExportFormat(format);
  }, []);

  // 处理质量选择
  const handleQualitySelect = useCallback((quality: string) => {
    setExportQuality(quality);
  }, []);

  // 渲染下载状态UI
  const renderDownloadingState = () => (
    <Box sx={COMMON_STYLES.flexColumnWithGap}>
      <Typography variant="body2">
        {downloadState.status === "completed"
          ? t("generation_complete")
          : downloadState.status === "failed"
          ? t("generation_failed")
          : downloadState.status === "cancelled"
          ? t("cancelled")
          : downloadState.stage
          ? `${t("generating_video")} (${getStageDisplayName(
              downloadState.stage,
              t
            )})`
          : t("generating_video")}
      </Typography>
      {downloadState.error && (
        <Typography variant="caption" color="error">
          {downloadState.error}
        </Typography>
      )}
      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <LinearProgress
          variant="determinate"
          value={downloadState.progress}
          sx={{
            flexGrow: 1,
            height: 8,
            borderRadius: 0.5,
            backgroundColor:
              downloadState.status === "failed" ? "error.light" : undefined,
          }}
        />
        <Box
          sx={{
            border: 1,
            borderColor: "divider",
            p: 0.5,
            borderRadius: 0.5,
            minWidth: 40,
            textAlign: "center",
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {Math.round(downloadState.progress)}%
          </Typography>
        </Box>
      </Box>
      {downloadState.status === "completed" ? (
        <Button
          variant="contained"
          size="small"
          fullWidth
          href={downloadState.downloadUrl}
          download
          onClick={handleDownloadVideo}
        >
          {t("download_video")}
        </Button>
      ) : downloadState.status === "failed" ? (
        <Button
          variant="contained"
          size="small"
          fullWidth
          onClick={handleExport}
          color="error"
        >
          {t("retry")}
        </Button>
      ) : downloadState.status === "cancelled" ? (
        <Button
          variant="contained"
          size="small"
          fullWidth
          onClick={handleExport}
        >
          {t("restart")}
        </Button>
      ) : (
        <Button
          variant="outlined"
          size="small"
          fullWidth
          onClick={() => cancelVideoProcessing(downloadState.renderId)}
        >
          {t("cancel")}
        </Button>
      )}
    </Box>
  );

  // 渲染导出设置UI
  const renderExportSettings = () => (
    <Box sx={COMMON_STYLES.flexColumnWithGap}>
      <Typography variant="body2" fontWeight="medium">
        {t("export_settings")}
      </Typography>

      <CustomPopover
        buttonContent={currentFormatLabel}
        buttonProps={{
          sx: {
            border: "1px solid",
            borderColor: "divider",
            borderRadius: 1,
            width: "100%",
            justifyContent: "space-between",
            py: 1,
          },
        }}
        buttonIcon={<KeyboardArrowDownIcon sx={{ ml: 1, opacity: 0.7 }} />}
        minWidth={120}
      >
        <Box sx={{ py: 1 }}>
          {FORMAT_OPTIONS.map((option) => (
            <Box
              key={option.value}
              sx={{
                px: 2,
                py: 1,
                cursor: "pointer",
                "&:hover": { bgcolor: "action.hover" },
                bgcolor:
                  exportFormat === option.value
                    ? "action.selected"
                    : "transparent",
              }}
              onClick={() => handleFormatSelect(option.value)}
            >
              <Typography variant="body2">{t(option.label)}</Typography>
            </Box>
          ))}
        </Box>
      </CustomPopover>

      <CustomPopover
        buttonContent={currentQualityLabel}
        buttonProps={{
          sx: {
            border: "1px solid",
            borderColor: "divider",
            borderRadius: 1,
            width: "100%",
            justifyContent: "space-between",
            py: 1,
          },
        }}
        buttonIcon={<KeyboardArrowDownIcon sx={{ ml: 1, opacity: 0.7 }} />}
        minWidth={120}
      >
        <Box sx={{ py: 1 }}>
          {QUALITY_OPTIONS.map((option) => (
            <Box
              key={option.value}
              sx={{
                px: 2,
                py: 1,
                cursor: "pointer",
                "&:hover": { bgcolor: "action.hover" },
                bgcolor:
                  exportQuality === option.value
                    ? "action.selected"
                    : "transparent",
              }}
              onClick={() => handleQualitySelect(option.value)}
            >
              <Box>
                <Typography variant="body2" fontWeight="medium">
                  {t(option.label)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {t(option.description)}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>
      </CustomPopover>

      <Button
        variant="contained"
        size="small"
        fullWidth
        onClick={handleExport}
        disabled={isExporting}
      >
        {isExporting ? t("preparing_export") : t("export")}
      </Button>
    </Box>
  );

  return (
    <CustomPopover
      customTrigger={
        <Tooltip title={t("export")} arrow>
          <IconButton disabled={isExporting}>
            <DownloadIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </Tooltip>
      }
      popoverProps={{
        anchorOrigin: {
          vertical: "bottom",
          horizontal: "right",
        },
        transformOrigin: {
          vertical: "top",
          horizontal: "right",
        },
      }}
      minWidth={280}
    >
      {downloadState.isDownloading
        ? renderDownloadingState()
        : renderExportSettings()}
    </CustomPopover>
  );
});

DownloadPopover.displayName = "DownloadPopover";
