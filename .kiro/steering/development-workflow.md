---
inclusion: manual
---
# 开发工作流程指南

## 开发环境设置
1. 系统要求：
   - Node.js >= 16.x
   - npm >= 8.x
   - FFmpeg >= 4.x
2. 环境配置：
   - 克隆项目后运行 `npm install` 安装依赖
   - 配置 `.env` 文件（参考 `.env.example`）
   - 确保 FFmpeg 正确安装并配置环境变量

## 开发流程
1. 分支管理：
   - 主分支：`main`
   - 开发分支：`develop`
   - 功能分支：`feature/功能名称`
   - 修复分支：`fix/问题描述`
2. 开发步骤：
   - 从 `develop` 分支创建新的功能分支
   - 在功能分支上进行开发
   - 定期提交代码，保持提交信息清晰
   - 完成功能后创建 Pull Request
   - 代码审查通过后合并到 `develop` 分支

## 测试要求
1. 前端测试：
   - 单元测试：使用 Jest + React Testing Library
   - 组件测试：覆盖核心组件功能
   - 集成测试：测试组件间交互
2. 后端测试：
   - 单元测试：使用 Jest
   - API 测试：使用 Supertest
   - FFmpeg 命令测试：验证命令生成正确性
3. 测试覆盖率要求：
   - 核心功能：>80%
   - 工具函数：>90%
   - API 接口：>85%

## 代码审查
1. 审查重点：
   - 代码质量和可维护性
   - 性能影响
   - 安全性考虑
   - 测试覆盖情况
2. 审查流程：
   - 至少需要一个审查者批准
   - 所有测试必须通过
   - 解决所有审查意见

## 部署流程
1. 开发环境：
   - 自动部署到开发服务器
   - 运行所有测试
   - 检查构建是否成功
2. 测试环境：
   - 手动触发部署
   - 进行功能测试
   - 性能测试
3. 生产环境：
   - 确认所有测试通过
   - 更新版本号
   - 生成构建文件
   - 部署到生产服务器
   - 监控系统运行状态

## 文档维护
1. 代码文档：
   - 及时更新 API 文档
   - 维护组件文档
   - 记录重要的架构决策
2. 项目文档：
   - 更新 README 文件
   - 维护更新日志
   - 记录已知问题和解决方案
3. 部署文档：
   - 记录部署步骤
   - 维护环境配置说明
   - 记录故障处理流程
