import * as os from "os";
import logger from "../utils/logger";

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  QUEUED = "queued",
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled"
}

/**
 * 任务信息接口
 */
export interface TaskInfo {
  taskId: string;
  status: TaskStatus;
  queuedAt: number;
  startedAt?: number;
  completedAt?: number;
  error?: string;
}

/**
 * 任务优先级枚举
 */
export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4
}

/**
 * 任务复杂度信息接口
 */
export interface TaskComplexity {
  elementCount: number;
  videoDuration: number;
  hasAudio: boolean;
  hasSubtitles: boolean;
  canvasSize: number;
  complexityScore: number;
}

/**
 * 队列任务接口
 */
interface QueueTask {
  taskId: string;
  execute: () => Promise<void>;
  queuedAt: number;
  priority: TaskPriority;
  complexity?: TaskComplexity;
}

/**
 * 任务队列类
 * 管理并发的视频生成任务，避免系统资源过载
 * 支持基于复杂度的优先级调度
 */
export class TaskQueue {
  /** 任务队列 */
  private queue: QueueTask[] = [];
  
  /** 正在运行的任务集合 */
  private running: Set<string> = new Set();
  
  /** 任务信息映射 */
  private taskInfo: Map<string, TaskInfo> = new Map();
  
  /** 最大并发任务数 */
  private maxConcurrent: number;
  
  /** 任务完成回调 */
  private onTaskComplete?: (taskId: string, success: boolean) => void;
  
  /**
   * 构造函数
   * @param maxConcurrentOverride 可选的最大并发任务数覆盖值
   */
  constructor(maxConcurrentOverride?: number) {
    // 根据CPU核心数设置最大并发任务数
    const cpuCount = os.cpus().length;
    
    // 如果提供了覆盖值，使用覆盖值；否则根据CPU核心数计算
    // 默认使用CPU核心数减1，但不少于1个，不多于4个
    this.maxConcurrent = maxConcurrentOverride !== undefined
      ? maxConcurrentOverride
      : Math.max(1, Math.min(cpuCount - 1, 4));
    
    logger.info(`任务队列初始化，最大并发任务数: ${this.maxConcurrent}`);
  }
  
  /**
   * 计算任务复杂度
   * @param canvasState 画布状态（可选）
   * @returns 任务复杂度信息
   */
  public calculateComplexity(canvasState?: any): TaskComplexity {
    if (!canvasState) {
      return {
        elementCount: 1,
        videoDuration: 10,
        hasAudio: false,
        hasSubtitles: false,
        canvasSize: 1920 * 1080,
        complexityScore: 1
      };
    }

    const elementCount = canvasState.elements?.length || 0;
    const videoDuration = this.calculateDuration(canvasState.elements || []);
    const hasAudio = this.hasAudioElements(canvasState.elements || []);
    const hasSubtitles = canvasState.captions?.length > 0;
    const canvasSize = (canvasState.width || 1920) * (canvasState.height || 1080);

    // 复杂度评分算法
    let score = 1;
    score += elementCount * 0.5; // 每个元素增加0.5分
    score += videoDuration * 0.1; // 每秒增加0.1分
    score += hasAudio ? 2 : 0; // 音频处理增加2分
    score += hasSubtitles ? 1 : 0; // 字幕增加1分
    score += (canvasSize / (1920 * 1080)) * 2; // 分辨率因子

    return {
      elementCount,
      videoDuration,
      hasAudio,
      hasSubtitles,
      canvasSize,
      complexityScore: Math.round(score * 100) / 100
    };
  }

  /**
   * 根据复杂度确定任务优先级
   * @param complexity 任务复杂度
   * @returns 任务优先级
   */
  public determinePriority(complexity: TaskComplexity): TaskPriority {
    const { complexityScore } = complexity;
    
    if (complexityScore <= 2) return TaskPriority.HIGH; // 简单任务优先
    if (complexityScore <= 5) return TaskPriority.NORMAL;
    if (complexityScore <= 10) return TaskPriority.LOW;
    return TaskPriority.LOW; // 复杂任务最后处理
  }

  /**
   * 将任务添加到队列
   * @param taskId 任务ID
   * @param execute 任务执行函数
   * @param canvasState 画布状态（用于计算复杂度）
   * @returns 队列中的位置（0表示立即执行）
   */
  public enqueue(taskId: string, execute: () => Promise<void>, canvasState?: any): number {
    // 检查任务是否已经在队列中
    if (this.isTaskQueued(taskId) || this.isTaskRunning(taskId)) {
      logger.warn(`任务 ${taskId} 已经在队列中或正在运行`);
      return this.getQueuePosition(taskId);
    }
    
    // 计算任务复杂度和优先级
    const complexity = this.calculateComplexity(canvasState);
    const priority = this.determinePriority(complexity);
    
    logger.info(`任务 ${taskId} 复杂度评分: ${complexity.complexityScore}, 优先级: ${TaskPriority[priority]}`);
    
    // 创建任务对象
    const task: QueueTask = {
      taskId,
      execute,
      queuedAt: Date.now(),
      priority,
      complexity
    };
    
    // 添加任务到队列并按优先级排序
    this.queue.push(task);
    this.sortQueue();
    
    // 更新任务信息
    this.taskInfo.set(taskId, {
      taskId,
      status: TaskStatus.QUEUED,
      queuedAt: Date.now()
    });
    
    const queuePosition = this.getQueuePosition(taskId);
    logger.info(`任务 ${taskId} 已添加到队列，当前位置: ${queuePosition}`);
    
    // 尝试处理队列
    this.processQueue();
    
    return queuePosition;
  }
  
  /**
   * 从队列中移除任务
   * @param taskId 任务ID
   * @returns 是否成功移除
   */
  public dequeue(taskId: string): boolean {
    // 如果任务正在运行，不能直接移除
    if (this.isTaskRunning(taskId)) {
      logger.warn(`任务 ${taskId} 正在运行，无法从队列中移除`);
      return false;
    }
    
    // 从队列中移除任务
    const initialLength = this.queue.length;
    this.queue = this.queue.filter(task => task.taskId !== taskId);
    
    // 如果任务被移除，更新任务信息
    if (initialLength > this.queue.length) {
      const info = this.taskInfo.get(taskId);
      if (info) {
        info.status = TaskStatus.CANCELLED;
        info.completedAt = Date.now();
      }
      logger.info(`任务 ${taskId} 已从队列中移除`);
      return true;
    }
    
    logger.warn(`任务 ${taskId} 不在队列中，无法移除`);
    return false;
  }
  
  /**
   * 标记任务完成
   * @param taskId 任务ID
   * @param success 是否成功完成
   */
  public markTaskComplete(taskId: string, success: boolean, error?: string): void {
    // 从运行集合中移除任务
    this.running.delete(taskId);
    
    // 更新任务信息
    const info = this.taskInfo.get(taskId);
    if (info) {
      info.status = success ? TaskStatus.COMPLETED : TaskStatus.FAILED;
      info.completedAt = Date.now();
      if (error) {
        info.error = error;
      }
    }
    
    // 调用完成回调
    if (this.onTaskComplete) {
      this.onTaskComplete(taskId, success);
    }
    
    logger.info(`任务 ${taskId} 已${success ? '成功' : '失败'}完成`);
    
    // 处理队列中的下一个任务
    this.processQueue();
  }
  
  /**
   * 设置任务完成回调
   * @param callback 回调函数
   */
  public setTaskCompleteCallback(callback: (taskId: string, success: boolean) => void): void {
    this.onTaskComplete = callback;
  }
  
  /**
   * 获取任务在队列中的位置
   * @param taskId 任务ID
   * @returns 队列位置（0表示正在运行，-1表示不在队列中）
   */
  public getQueuePosition(taskId: string): number {
    // 如果任务正在运行，返回0
    if (this.isTaskRunning(taskId)) {
      return 0;
    }
    
    // 查找任务在队列中的位置
    const index = this.queue.findIndex(task => task.taskId === taskId);
    return index >= 0 ? index + 1 : -1;
  }
  
  /**
   * 获取任务信息
   * @param taskId 任务ID
   * @returns 任务信息，如果不存在则返回undefined
   */
  public getTaskInfo(taskId: string): TaskInfo | undefined {
    return this.taskInfo.get(taskId);
  }
  
  /**
   * 获取所有任务信息
   * @returns 所有任务信息的数组
   */
  public getAllTaskInfo(): TaskInfo[] {
    return Array.from(this.taskInfo.values());
  }
  
  /**
   * 获取队列统计信息
   * @returns 队列统计信息
   */
  public getQueueStats(): {
    totalQueued: number;
    totalRunning: number;
    priorityBreakdown: Record<string, number>;
    averageComplexity: number;
    estimatedWaitTime: number;
  } {
    const priorityBreakdown: Record<string, number> = {
      [TaskPriority[TaskPriority.URGENT]]: 0,
      [TaskPriority[TaskPriority.HIGH]]: 0,
      [TaskPriority[TaskPriority.NORMAL]]: 0,
      [TaskPriority[TaskPriority.LOW]]: 0
    };

    let totalComplexity = 0;
    for (const task of this.queue) {
      const priorityName = TaskPriority[task.priority];
      priorityBreakdown[priorityName]++;
      totalComplexity += task.complexity?.complexityScore || 0;
    }

    const averageComplexity = this.queue.length > 0 ? totalComplexity / this.queue.length : 0;
    
    // 估算等待时间（基于平均复杂度）
    const avgProcessingTime = averageComplexity * 30; // 每个复杂度分数估算30秒
    const estimatedWaitTime = Math.round(avgProcessingTime * this.queue.length / this.maxConcurrent);

    return {
      totalQueued: this.queue.length,
      totalRunning: this.running.size,
      priorityBreakdown,
      averageComplexity: Math.round(averageComplexity * 100) / 100,
      estimatedWaitTime
    };
  }

  /**
   * 获取指定优先级的任务数量
   * @param priority 优先级
   * @returns 任务数量
   */
  public getTaskCountByPriority(priority: TaskPriority): number {
    return this.queue.filter(task => task.priority === priority).length;
  }

  /**
   * 获取队列中最高优先级任务
   * @returns 最高优先级任务或undefined
   */
  public getNextTask(): QueueTask | undefined {
    this.sortQueue();
    return this.queue[0];
  }

  /**
   * 清理旧的任务信息
   * @param maxAgeHours 最大保留时间（小时）
   */
  public cleanupOldTasks(maxAgeHours: number = 24): void {
    const now = Date.now();
    const maxAgeMs = maxAgeHours * 3600000; // 转换为毫秒
    const cutoffTime = now - maxAgeMs;
    
    // 找出需要清理的任务
    const tasksToCleanup: string[] = [];
    
    for (const [taskId, info] of this.taskInfo.entries()) {
      // 只清理已完成或失败的任务
      if (
        (info.status === TaskStatus.COMPLETED || 
         info.status === TaskStatus.FAILED || 
         info.status === TaskStatus.CANCELLED) && 
        info.completedAt && 
        info.completedAt < cutoffTime
      ) {
        tasksToCleanup.push(taskId);
      }
    }
    
    // 清理任务信息
    for (const taskId of tasksToCleanup) {
      this.taskInfo.delete(taskId);
    }
    
    if (tasksToCleanup.length > 0) {
      logger.info(`已清理 ${tasksToCleanup.length} 个旧任务信息`);
      
      // 记录清理后的队列统计
      const stats = this.getQueueStats();
      logger.info(`当前队列状态: 排队${stats.totalQueued}个, 运行${stats.totalRunning}个, 平均复杂度${stats.averageComplexity}`);
    }
  }
  
  /**
   * 动态调整最大并发数（基于系统负载）
   * @param systemLoad 系统负载百分比 (0-100)
   */
  public adjustConcurrency(systemLoad: number): void {
    const cpuCount = os.cpus().length;
    let newMaxConcurrent: number;
    
    if (systemLoad < 50) {
      // 低负载，可以增加并发数
      newMaxConcurrent = Math.min(cpuCount, 6);
    } else if (systemLoad < 80) {
      // 中等负载，保持默认并发数
      newMaxConcurrent = Math.max(1, Math.min(cpuCount - 1, 4));
    } else {
      // 高负载，减少并发数
      newMaxConcurrent = Math.max(1, Math.min(cpuCount - 2, 2));
    }
    
    if (newMaxConcurrent !== this.maxConcurrent) {
      logger.info(`根据系统负载(${systemLoad}%)调整最大并发数: ${this.maxConcurrent} -> ${newMaxConcurrent}`);
      this.maxConcurrent = newMaxConcurrent;
    }
  }

  /**
   * 检查任务是否在队列中
   * @param taskId 任务ID
   * @returns 是否在队列中
   */
  private isTaskQueued(taskId: string): boolean {
    return this.queue.some(task => task.taskId === taskId);
  }
  
  /**
   * 检查任务是否正在运行
   * @param taskId 任务ID
   * @returns 是否正在运行
   */
  private isTaskRunning(taskId: string): boolean {
    return this.running.has(taskId);
  }
  
  /**
   * 按优先级和复杂度排序队列
   */
  private sortQueue(): void {
    this.queue.sort((a, b) => {
      // 首先按优先级排序（数值越大优先级越高）
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      
      // 相同优先级下，简单任务优先（复杂度越低越优先）
      const aScore = a.complexity?.complexityScore || 0;
      const bScore = b.complexity?.complexityScore || 0;
      if (aScore !== bScore) {
        return aScore - bScore;
      }
      
      // 最后按入队时间排序（FIFO）
      return a.queuedAt - b.queuedAt;
    });
  }

  /**
   * 计算元素总时长
   */
  private calculateDuration(elements: any[]): number {
    if (!elements.length) return 10;
    
    const maxEndTime = Math.max(...elements.map(el => 
      el.timeFrame?.end || el.duration || 10000
    ));
    return maxEndTime / 1000; // 转换为秒
  }

  /**
   * 检查是否包含音频元素
   */
  private hasAudioElements(elements: any[]): boolean {
    return elements.some(el => 
      el.type === 'audio' || 
      (el.type === 'video' && el.hasAudio !== false)
    );
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    // 如果没有任务或已达到最大并发数，直接返回
    if (this.queue.length === 0 || this.running.size >= this.maxConcurrent) {
      return;
    }
    
    // 重新排序队列以确保优先级正确
    this.sortQueue();
    
    // 获取队列中的下一个任务（优先级最高的）
    const task = this.queue.shift();
    if (!task) return;
    
    // 将任务添加到运行集合
    this.running.add(task.taskId);
    
    // 更新任务信息
    const info = this.taskInfo.get(task.taskId);
    if (info) {
      info.status = TaskStatus.RUNNING;
      info.startedAt = Date.now();
    }
    
    const priorityName = TaskPriority[task.priority];
    const complexityScore = task.complexity?.complexityScore || 0;
    logger.info(`开始执行任务 ${task.taskId} [优先级: ${priorityName}, 复杂度: ${complexityScore}]，队列中剩余 ${this.queue.length} 个任务`);
    
    // 执行任务
    try {
      await task.execute();
      // 任务成功完成
      this.markTaskComplete(task.taskId, true);
    } catch (error) {
      // 任务执行失败
      const errorMessage = error instanceof Error ? error.message : "未知错误";
      logger.error(`任务 ${task.taskId} 执行失败: ${errorMessage}`);
      this.markTaskComplete(task.taskId, false, errorMessage);
    }
  }
}
