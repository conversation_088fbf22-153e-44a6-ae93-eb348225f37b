"use client";
import React from "react";
import { StoreContext } from "../../store";
import { observer } from "mobx-react";
import AddIcon from "@mui/icons-material/Add";
import { Box, Typography, Button, Paper } from "@mui/material";

export const TextResource = observer(({ fontSize, fontWeight, sampleText }) => {
  const store = React.useContext(StoreContext);

  return (
    <Button
      onClick={() => {
        store.addText({
          text: sampleText,
          fontSize: fontSize,
          fontWeight: fontWeight,
        });
        store.saveChange("add_element");
      }}
      variant="contained"
      size="small"
      fullWidth
      sx={{
        textTransform: "none",
        bgcolor: "action.selected",
        "&:hover": {
          bgcolor: "action.selectedHover",
        },
      }}
    >
      <Typography
        sx={{
          flexGrow: 1,
          color: "text.primary",
          px: 1,
          py: 0.5,
          fontSize: `${fontSize}px`,
          fontWeight: fontWeight,
        }}
      >
        {sampleText}
      </Typography>
    </Button>
  );
});
