import React, { useState, useEffect } from "react";
import {
  <PERSON>nackbar,
  Alert,
  Box,
  LinearProgress,
  IconButton,
  SnackbarOrigin,
  Chip,
  keyframes,
  styled,
} from "@mui/material";
import {
  Close as CloseIcon,
  CloudDownload as DownloadIcon,
  Refresh as RefreshIcon,
  Memory as ProcessingIcon,
  CloudUpload as UploadIcon,
  AutoFixHigh as MagicIcon,
} from "@mui/icons-material";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../store";
import { useLanguage } from "../i18n/LanguageContext";

// 旋转动画
const spinAnimation = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`;

// 呼吸效果动画
const pulseAnimation = keyframes`
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
`;

// 滑动动画
const slideAnimation = keyframes`
  0% {
    transform: translateX(5px);
    opacity: 0.7;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
`;

// 样式化的加载图标容器
const StyledIconContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  marginRight: theme.spacing(1.5),
  "& .loading-icon": {
    animation: `${spinAnimation} 2s linear infinite`,
    color: theme.palette.primary.contrastText,
  },
  "& .pulse-icon": {
    animation: `${pulseAnimation} 1.5s ease-in-out infinite`,
    color: theme.palette.primary.contrastText,
  },
}));

// 样式化的计数标签
const StyledCountChip = styled(Chip)(({ theme }) => ({
  height: 20,
  fontSize: "0.75rem",
  fontWeight: 600,
  backgroundColor: "rgba(255, 255, 255, 0.2)",
  color: theme.palette.primary.contrastText,
  "& .MuiChip-label": {
    paddingLeft: theme.spacing(0.75),
    paddingRight: theme.spacing(0.75),
  },
  animation: `${slideAnimation} 0.3s ease-out`,
}));

// 样式化的阶段信息
const StyledStageBox = styled(Box)(({ theme }) => ({
  fontSize: "0.75rem",
  opacity: 0.9,
  fontStyle: "italic",
  animation: `${slideAnimation} 0.5s ease-out`,
  color: theme.palette.primary.contrastText,
}));

export interface LoadingSnackbarProps {
  /**
   * 自定义位置，默认右下角
   */
  position?: SnackbarOrigin;
}

/**
 * 统一的加载状态提示组件
 * 智能检测和显示加载状态：
 * - 优先显示全局加载状态（更详细的信息）
 * - 当无全局加载时，显示基础加载状态
 * - 避免多个加载提示同时出现
 */
const UnifiedLoadingSnackbar: React.FC<LoadingSnackbarProps> = observer(
  ({
    position = { vertical: "bottom", horizontal: "right" } as SnackbarOrigin,
  }) => {
    const store = React.useContext(StoreContext);
    const { t } = useLanguage();
    const [open, setOpen] = useState(false);
    const [dismissed, setDismissed] = useState(false);

    // 智能检测加载状态：优先使用全局加载，其次使用基础加载
    const hasGlobalLoading = store.hasGlobalLoading;
    const hasBasicLoading = store.isLoading;
    const isLoading = hasGlobalLoading || hasBasicLoading;

    // 确定当前使用的模式
    const isGlobalMode = hasGlobalLoading;

    // 记录一次全局加载会话中的“总元素数量”（使用出现过的最大值作为总数）
    const globalTotalRef = React.useRef<number>(0);

    // 当进入/更新全局加载时，更新总数；当退出时重置
    useEffect(() => {
      const currentCount = (() => {
        try {
          return store.loadingElements ? store.loadingElements.size : 0;
        } catch {
          return 0;
        }
      })();

      if (hasGlobalLoading) {
        // 将出现过的最大未完成数作为本次会话的总数
        if (currentCount > globalTotalRef.current) {
          globalTotalRef.current = currentCount;
        }
      } else {
        // 退出全局加载时重置
        globalTotalRef.current = 0;
      }
    }, [hasGlobalLoading, store.loadingElements]);

    // 控制显示/隐藏逻辑
    useEffect(() => {
      if (isLoading) {
        if (!dismissed) setOpen(true);
      } else {
        setOpen(false);
        setDismissed(false);
      }
    }, [isLoading, dismissed]);

    const handleClose = () => {
      setOpen(false);
      setDismissed(true);
    };

    // 智能获取消息和数据 - 统一处理基础和全局加载
    const getMessage = () => {
      if (isGlobalMode) {
        return store.globalLoadingMessage || t("loading_elements");
      }

      // 基础加载智能处理消息
      const rawMessage = store.loadingMessage || "";

      // 如果消息为空或是默认消息，提供更友好的描述
      if (!rawMessage || rawMessage.match(/^(loading|processing)\.{0,3}$/i)) {
        return t("loading_snackbar_processing_task");
      }

      // 优化常见的技术术语为用户友好的描述
      const friendlyMessages: { [key: string]: string } = {
        saving: t("action_saving"),
        loading: t("action_loading"),
        processing: t("action_processing"),
        uploading: t("action_uploading"),
        downloading: t("action_downloading"),
        rendering: t("action_rendering"),
        generating: t("action_generating"),
        importing: t("action_importing"),
        exporting: t("action_exporting"),
        "completing import": t("action_completing_import"),
        refreshing: t("action_refreshing"),
        updating: t("action_updating"),
        creating: t("action_creating"),
        deleting: t("action_deleting"),
      };

      const lowerMessage = rawMessage.toLowerCase();

      // 尝试匹配友好消息
      for (const [key, friendlyText] of Object.entries(friendlyMessages)) {
        if (lowerMessage.includes(key)) {
          return friendlyText;
        }
      }

      // 如果没有匹配，返回原消息（但限制长度）
      return rawMessage.length > 30
        ? rawMessage.substring(0, 30) + "..."
        : rawMessage;
    };

    const getLoadingCount = () => {
      if (isGlobalMode) {
        try {
          return store.loadingElements ? store.loadingElements.size : 0;
        } catch {
          return 0;
        }
      }
      // 基础加载显示为1项
      return hasBasicLoading ? 1 : 0;
    };

    const getProgress = () => {
      if (isGlobalMode) {
        // 1) 若外部提供了明确进度，优先使用
        if (typeof store.globalLoadingProgress === "number") {
          return Math.max(0, Math.min(100, store.globalLoadingProgress));
        }

        // 2) 否则按“总数-当前未完成数”的比例估算进度
        const currentCount = (() => {
          try {
            return store.loadingElements ? store.loadingElements.size : 0;
          } catch {
            return 0;
          }
        })();

        // 若总数尚未建立，但当前有未完成项，则将当前值作为总数起点
        const total = globalTotalRef.current || currentCount;
        if (total > 0) {
          const done = Math.max(0, total - currentCount);
          const percent = Math.floor((done / total) * 100);
          return Math.max(0, Math.min(100, percent));
        }

        // 无法判断时返回不确定
        return null;
      }

      // ===== 基础加载进度推断逻辑 =====
      const rawMessage = store.loadingMessage || "";
      const lowerMessage = rawMessage.toLowerCase();

      // 1) 直接解析消息中的百分比（优先级最高）
      const percentMatch = lowerMessage.match(/(\d+(?:\.\d+)?)%/);
      if (percentMatch) {
        const value = parseFloat(percentMatch[1]);
        if (!Number.isNaN(value)) {
          return Math.max(0, Math.min(100, value));
        }
      }

      // 2) 根据常见阶段关键词映射一个大致百分比
      const phaseToProgress: Array<{ keys: string[]; progress: number }> = [
        { keys: ["init", "初始化", "starting", "start"], progress: 5 },
        { keys: ["prepare", "准备", "queu", "排队"], progress: 10 },
        { keys: ["upload", "上传"], progress: 20 },
        { keys: ["download", "下载"], progress: 25 },
        { keys: ["load", "加载"], progress: 30 },
        { keys: ["parse", "解析"], progress: 35 },
        { keys: ["process", "处理", "analy", "analysis"], progress: 50 },
        { keys: ["ai", "generate", "生成"], progress: 60 },
        { keys: ["render", "渲染"], progress: 75 },
        { keys: ["save", "保存", "write", "写入"], progress: 85 },
        { keys: ["final", "完成", "finish", "done"], progress: 95 },
      ];

      for (const { keys, progress } of phaseToProgress) {
        if (keys.some((k) => lowerMessage.includes(k))) {
          return progress;
        }
      }

      // 3) 若能获取到元素加载比例，则按比例估算
      try {
        const total = store.editorElements?.length ?? 0;
        if (total > 0) {
          const loaded = store.editorElements.filter(
            (el: any) =>
              el && el.fabricObject !== undefined && el.fabricObject !== null
          ).length;
          // 若全部加载完成，则返回100，否则按比例估算（向下取整，避免超前）
          return loaded >= total ? 100 : Math.floor((loaded / total) * 100);
        }
      } catch {}

      // 4) 无法判断时返回不确定进度
      return null;
    };

    const getStage = () => {
      if (isGlobalMode) {
        return store.globalLoadingStage || "";
      }
      // 基础加载从loadingMessage中智能提取阶段信息
      const message = store.loadingMessage || "";
      const lowerMessage = message.toLowerCase();

      // 检测常见的操作类型并生成友好的阶段描述
      if (lowerMessage.includes("save") || lowerMessage.includes("saving")) {
        return t("stage_saving");
      }
      if (lowerMessage.includes("load") || lowerMessage.includes("loading")) {
        return t("stage_loading");
      }
      if (
        lowerMessage.includes("process") ||
        lowerMessage.includes("processing")
      ) {
        return t("stage_processing");
      }
      if (
        lowerMessage.includes("upload") ||
        lowerMessage.includes("uploading")
      ) {
        return t("stage_uploading");
      }
      if (
        lowerMessage.includes("download") ||
        lowerMessage.includes("downloading")
      ) {
        return t("stage_downloading");
      }
      if (
        lowerMessage.includes("render") ||
        lowerMessage.includes("rendering")
      ) {
        return t("stage_rendering");
      }
      if (
        lowerMessage.includes("generate") ||
        lowerMessage.includes("generating")
      ) {
        return t("stage_generating");
      }
      if (
        lowerMessage.includes("import") ||
        lowerMessage.includes("importing")
      ) {
        return t("stage_importing");
      }
      if (
        lowerMessage.includes("export") ||
        lowerMessage.includes("exporting")
      ) {
        return t("stage_exporting");
      }

      // 如果消息有实际内容且不是默认的"Loading..."，则显示为阶段
      if (message && !message.match(/^(loading|processing)\.{0,3}$/i)) {
        return message.length > 20 ? message.substring(0, 20) + "..." : message;
      }

      return "";
    };

    const message = getMessage();
    const loadingCount = getLoadingCount();
    const progress = getProgress();
    const stage = getStage();

    const showDeterminate = typeof progress === "number";
    const progressValue = showDeterminate
      ? Math.max(0, Math.min(100, progress))
      : 0;

    // 智能选择图标
    const getLoadingIcon = () => {
      if (isGlobalMode) {
        // 根据阶段或消息内容选择图标
        const lowerMessage = (message || "").toLowerCase();
        const lowerStage = (stage || "").toLowerCase();

        if (lowerMessage.includes("upload") || lowerStage.includes("upload")) {
          return <UploadIcon className="loading-icon" fontSize="small" />;
        }
        if (
          lowerMessage.includes("download") ||
          lowerStage.includes("download")
        ) {
          return <DownloadIcon className="loading-icon" fontSize="small" />;
        }
        if (
          lowerMessage.includes("process") ||
          lowerStage.includes("process")
        ) {
          return <ProcessingIcon className="loading-icon" fontSize="small" />;
        }
        if (lowerMessage.includes("ai") || lowerMessage.includes("generate")) {
          return <MagicIcon className="pulse-icon" fontSize="small" />;
        }

        // 默认使用处理图标
        return <ProcessingIcon className="loading-icon" fontSize="small" />;
      } else {
        // 基础加载使用刷新图标
        return <RefreshIcon className="loading-icon" fontSize="small" />;
      }
    };

    return (
      <Snackbar
        open={open}
        onClose={handleClose}
        anchorOrigin={position}
        autoHideDuration={null} // 持续展示，直到用户关闭或加载结束
        TransitionProps={{
          timeout: { enter: 300, exit: 200 },
        }}
        sx={{
          "& .MuiSnackbar-root": {
            maxWidth: { xs: "calc(100vw - 32px)", sm: 480 },
          },
        }}
      >
        <Alert
          severity="info"
          variant="filled"
          icon={false} // 关闭默认图标，使用自定义图标
          sx={{
            width: "100%",
            minWidth: 300,
            m: 2,

            backgroundColor: "primary.main",
            color: "primary.contrastText",
            borderRadius: 2,
            backdropFilter: "blur(8px)",
            "& .MuiAlert-message": {
              width: "100%",
              padding: 0,
            },
          }}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={handleClose}
              sx={{
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                },
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          }
        >
          {/* 自定义标题行 */}
          <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
            <StyledIconContainer>{getLoadingIcon()}</StyledIconContainer>
            <Box
              sx={{
                fontSize: "1rem",
                fontWeight: 500,
                flex: 1,
              }}
            >
              {isGlobalMode
                ? t("loading_snackbar_global_title")
                : t("loading_snackbar_basic_title")}
            </Box>
            {/* {showDeterminate && (
              <Box
                sx={{
                  fontSize: "0.75rem",
                  opacity: 0.9,
                  fontWeight: 500,
                }}
              >
                {progressValue}%
              </Box>
            )} */}
          </Box>

          {/* 内容区域 */}
          <Box sx={{ display: "flex", flexDirection: "column", gap: 0.75 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Box
                sx={{
                  fontSize: "0.875rem",
                  lineHeight: 1.4,
                  wordBreak: "break-word",
                  flex: 1,
                  opacity: 0.95,
                }}
              >
                {message}
              </Box>
              {loadingCount > 0 && isGlobalMode && (
                <StyledCountChip
                  label={t("items_count").replace("{0}", String(loadingCount))}
                  size="small"
                />
              )}
            </Box>
            {/* {!!stage && <StyledStageBox>📍 {stage}</StyledStageBox>} */}
          </Box>
          <LinearProgress
            variant={showDeterminate ? "determinate" : "indeterminate"}
            value={progressValue}
            sx={{
              mt: 1,
              mb: 1.5,
              height: 6,
              borderRadius: 3,
              backgroundColor: "rgba(255, 255, 255, 0.2)",
              "& .MuiLinearProgress-bar": {
                backgroundColor: "primary.contrastText",
                borderRadius: 3,
                boxShadow: "0 0 10px rgba(255, 255, 255, 0.3)",
                transition: "transform 0.3s ease-in-out",
              },
              "& .MuiLinearProgress-bar1Indeterminate": {
                animation:
                  "MuiLinearProgress-keyframes-indeterminate1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite",
              },
              "& .MuiLinearProgress-bar2Indeterminate": {
                animation:
                  "MuiLinearProgress-keyframes-indeterminate2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite",
              },
            }}
          />
        </Alert>
      </Snackbar>
    );
  }
);

export default UnifiedLoadingSnackbar;
